package com.saida.services.system.callback;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.algorithm.entity.AlgAlgorithmReviewConfigEntity;
import com.saida.services.algorithm.entity.ThirdPartyDeviceEntity;
import com.saida.services.algorithm.entity.ThirdPartyEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.ImageCropperUtil;
import com.saida.services.common.tools.LocalUrlUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.constant.RedisConstants;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.system.alarm.entity.AlarmEntity;
import com.saida.services.system.alarm.entity.AlarmManageEntity;
import com.saida.services.system.alarm.mapper.AlarmManageMapper;
import com.saida.services.system.alarm.service.AlarmService;
import com.saida.services.system.algorithm.service.AlgorithmReviewConfigService;
import com.saida.services.system.basicData.service.BasicPeopleFaceFeatureService;
import com.saida.services.system.basicData.service.BasicPeopleInfoService;
import com.saida.services.system.basicData.vo.PeopleRelateVo;
import com.saida.services.system.face.dto.FaceDto;
import com.saida.services.system.face.dto.MatchResult;
import com.saida.services.system.peopleDeployControl.entity.AlarmPeopleControlCompareRecord;
import com.saida.services.system.peopleDeployControl.entity.DeployControlGroupRef;
import com.saida.services.system.peopleDeployControl.entity.PeopleIdentifyDeployControlEntity;
import com.saida.services.system.peopleDeployControl.mapper.AlarmPeopleControlCompareRecordMapper;
import com.saida.services.system.peopleDeployControl.mapper.DeployControlGroupRefMapper;
import com.saida.services.system.peopleDeployControl.service.PeopleIdentifyDeployControlService;
import com.saida.services.system.peopleDeployControl.vo.PeopleDeployControlFaceCompareVo;
import com.saida.services.system.sys.service.ThirdPartyDeviceService;
import com.saida.services.system.sys.service.ThirdPartyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CallBackInvoke {

    @Resource
    private ThirdPartyService thirdPartyService;
    @Resource
    private ThirdPartyDeviceService thirdPartyDeviceService;
    @Resource
    private CallBackFeign callBackFeign;
    @Resource
    private AlgorithmReviewConfigService algorithmReviewConfigService;
    @Resource
    private AlarmManageMapper alarmManageMapper;
    @Resource
    private AlarmService alarmService;
    @Resource
    private PeopleIdentifyDeployControlService peopleIdentifyDeployControlService;
    @Autowired
    private BasicPeopleFaceFeatureService basicPeopleFaceFeatureService;
    @Resource
    private DeployControlGroupRefMapper deployControlGroupRefMapper;
    @Resource
    private BasicPeopleInfoService basicPeopleInfoService;
    @Resource
    private AlarmPeopleControlCompareRecordMapper alarmPeopleControlCompareRecordMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private FileService fileService;

    @Transactional(rollbackFor = Exception.class)
    public void callBack(AlarmEntity alarmEntity, CallBackMessage message, boolean enableReview, boolean saveAlarm) {
        alarmEntity.setPush(1);
        try {
            log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，程序开始...message={}, 【{}】", JSON.toJSON(message), saveAlarm ? "非人工审核" : "人工审核");
            if (message == null || message.getDeviceCode() == null || message.getAlertType() == null) {
                return;
            }
            // 人员布控
            List<PeopleIdentifyDeployControlEntity> deployControlList = peopleIdentifyDeployControlService.queryIsExist(alarmEntity);
            if (CollectionUtil.isNotEmpty(deployControlList)) {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                log.info("V-LINKER算法平台.接收第三方平台告警，开始处理人员布控数据...deployControlList={}", JSON.toJSON(deployControlList));
                dealAlarmByPeopleDeployControl(alarmEntity, deployControlList, message);
                stopWatch.stop();
                log.info("V-LINKER算法平台.接收第三方平台告警，人员布控数据处理完成，...耗时={}", stopWatch.getTotalTimeMillis());
            }
            if (enableReview) {
                // 判断是否进入审核流程，是否需要进行推送
                boolean needReview;
                AlgAlgorithmReviewConfigEntity algAlgorithmReviewConfigEntity = algorithmReviewConfigService.getAny(new LambdaQueryWrapper<>());
                if (Objects.nonNull(algAlgorithmReviewConfigEntity)) {
                    log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，告警审核配置不为空，判断是否需要审核...algAlgorithmReviewConfigEntity={}", JSON.toJSON(algAlgorithmReviewConfigEntity));
                    needReview = this.needReview(message, algAlgorithmReviewConfigEntity);
                    if (needReview) {
                        log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，符合审核规则，进入审核流程...algAlgorithmReviewConfigEntity={}", JSON.toJSON(algAlgorithmReviewConfigEntity));
                        AlarmManageEntity alarmManageEntity = new AlarmManageEntity();
                        alarmManageEntity.setAlarmId(alarmEntity.getId());
                        alarmManageEntity.setStatus(2);
                        alarmManageEntity.setCreateTime(new Date());
                        alarmManageEntity.setUpdateTime(new Date());
                        alarmManageMapper.insert(alarmManageEntity);
                        log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，符合审核规则，保存审核数据...程序结束");
                        alarmEntity.setPush(0);
                        return;
                    }
                }
            }

            if (StringUtil.isEmpty(message.getMsgId())) {
                message.setMsgId(UUID.randomUUID().toString());
            }
            PushAlarmDto pushAlarmDto = this.getPushAlarmDto(message);
            pushAlarmDto.setMsgType(1);
            log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据...message={}, pushAlarmDto={}", JSON.toJSON(message), JSON.toJSON(pushAlarmDto));

            callBackFeign.feignPush(pushAlarmDto);

            List<ThirdPartyDeviceEntity> thirdPartyDeviceEntityList = thirdPartyDeviceService.list(new LambdaQueryWrapper<ThirdPartyDeviceEntity>()
                    .eq(ThirdPartyDeviceEntity::getCameraId, message.getDeviceId())
                    .like(ThirdPartyDeviceEntity::getSubscribe, message.getAlertType())
                    .groupBy(ThirdPartyDeviceEntity::getThirdId)
            );
            if (CollectionUtil.isEmpty(thirdPartyDeviceEntityList)) {
                log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，第三方平台设备或告警订阅为空，程序结束...message={}", JSON.toJSON(message));
                return;
            }
            Set<Long> thirdIdSet = thirdPartyDeviceEntityList.stream().map(ThirdPartyDeviceEntity::getThirdId).collect(Collectors.toSet());

            List<ThirdPartyEntity> thirdPartyEntityList = thirdPartyService.list(new LambdaQueryWrapper<ThirdPartyEntity>()
                    .in(ThirdPartyEntity::getId, thirdIdSet)
                    .eq(ThirdPartyEntity::getStatus, 1)
                    .eq(ThirdPartyEntity::getFeignReq, 0)
                    .eq(ThirdPartyEntity::getInnerSystem, 0)
            );
            if (CollectionUtil.isEmpty(thirdPartyEntityList)) {
                log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，第三方平台为空，程序结束...message={}, thirdIdSet={}", JSON.toJSON(message), thirdIdSet);
                return;
            }

            // 遍历
            for (ThirdPartyEntity thirdPartyEntity : thirdPartyEntityList) {
                try {
                    log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据...thirdPartyEntity={}", JSON.toJSON(thirdPartyEntity));
                    if (!LocalUrlUtil.validUrl(thirdPartyEntity.getPushUrl())) {
                        log.info("V-LINKER算法中台.向第三方推送告警..pushUrl不合法..程序continue...thirdPartyEntity={}", JSON.toJSON(thirdPartyEntity));
                        continue;
                    }
                    callBackFeign.httpPush(thirdPartyEntity, pushAlarmDto);
                } catch (Exception e) {
                    log.info("V-LINKER算法中台.向第三方推送告警，错误..程序continue...thirdPartyEntity={}, message={}, msg={}", JSON.toJSON(thirdPartyEntity), JSON.toJSON(message), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("V-LINKER算法平台.接收第三方平台告警..推送告警数据，错误....message={}, msg={}", JSON.toJSON(message), e.getMessage(), e);
        } finally {
            if (saveAlarm) {
                alarmService.save(alarmEntity);
            }
            log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，推送完成，程序结束....message={}", JSON.toJSON(message));
        }
    }


    /**
     * 人脸识别告警对比布控记录生成人脸比对告警及比对记录
     * 每一条布控记录就生成最多一条人脸对比的告警数据，每个人脸对比告警数据关联最多n条人脸对比记录（n=告警图片提取的人脸数量）
     * 缓存人员告警数据，
     *
     * @param alarmEntity       告警实体
     * @param deployControlList 满足条件的布控记录
     */
    private void dealAlarmByPeopleDeployControl(AlarmEntity alarmEntity,
                                                List<PeopleIdentifyDeployControlEntity> deployControlList,
                                                CallBackMessage message) {
        boolean hasAlarm = false;
        String dateFormat = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
        String imgUrl = alarmEntity.getOriginalImageUrl();
        if (StringUtil.isEmpty(imgUrl)) {
            imgUrl = alarmEntity.getAlarmImageUrl();
        }
        if (StringUtil.isEmpty(imgUrl)) {
            log.info("V-LINKER算法平台.人脸识别告警, 源告警id:{} 没有图片就不解析了", alarmEntity.getId());
            return;
        }
        AlarmNormalizationExt alarmNormalizationExt = new AlarmNormalizationExt();
        String normalizationExt = alarmEntity.getNormalizationExt();
        if (StringUtil.isNotEmpty(normalizationExt)) {
            alarmNormalizationExt = JSON.parseObject(normalizationExt, AlarmNormalizationExt.class);
        }
        String[] imgUrls = imgUrl.split(",");
        List<FaceDto> allFaceDtos = new ArrayList<>();
        for (String url : imgUrls) {
            // 下载告警图片并提取人脸
            byte[] imageBytes = fileService.downloadFileAsByteArrayByCount(url, 5);
            if (imageBytes == null || imageBytes.length == 0) {
                log.error("V-LINKER算法平台.人脸识别告警, 源告警id:{},src={} 下载告警图片失败，结束处理", alarmEntity.getId(), url);
                return;
            }
            String base64AlarmPic = Base64.getEncoder().encodeToString(imageBytes);
            DtoResult<List<FaceDto>> faceResult = basicPeopleFaceFeatureService.saidaFaceFeatures(null, base64AlarmPic);
            log.info("V-LINKER算法平台.人脸识别告警, 源告警id:{}, 人脸识别结果:{}", alarmEntity.getId(), faceResult.success());
            if (!faceResult.success() || CollectionUtil.isEmpty(faceResult.getData())) {
                log.info("V-LINKER算法平台.人脸识别告警, 源告警id:{}, 图片未识别到人脸，结束处理", alarmEntity.getId());
                return;
            }
            List<FaceDto> faceDtos = faceResult.getData();
            faceDtos.forEach(faceDto -> {
                faceDto.setPhotoUrlBase64(base64AlarmPic);
            });
            allFaceDtos.addAll(faceDtos);
        }


        // 枚举所有布控任务绑定的布控分组
        Map<Long, List<PeopleIdentifyDeployControlEntity>> deployControlIdMap =
                deployControlList.stream().collect(Collectors.groupingBy(PeopleIdentifyDeployControlEntity::getId));
        List<DeployControlGroupRef> groupRefs = deployControlGroupRefMapper.selectList(new LambdaQueryWrapper<DeployControlGroupRef>()
                .in(DeployControlGroupRef::getDeployControlId, deployControlList.stream().map(PeopleIdentifyDeployControlEntity::getId).collect(Collectors.toList())));
        if (CollectionUtil.isEmpty(groupRefs)) {
            log.info("V-LINKER算法平台.人脸识别告警, 源告警id:{}, 未查询到布控分组数据", alarmEntity.getId());
            return;
        }
        // 映射 deployControlId -> groupIds
        Map<Long, List<Long>> groupIdMap = groupRefs.stream()
                .collect(Collectors.groupingBy(DeployControlGroupRef::getDeployControlId,
                        Collectors.mapping(DeployControlGroupRef::getGroupId, Collectors.toList())));
        // key = faceDto.hashCode，避免对同一张人脸重复裁剪上传
        Map<Integer, String> croppedFacePhotoUrlMap = new HashMap<>();
        for (Long deployControlId : groupIdMap.keySet()) {
            PeopleIdentifyDeployControlEntity deployControl = deployControlIdMap.get(deployControlId).get(0);

            int interval = deployControl.getSamePersonAlarmInterval();
            switch (deployControl.getSamePersonAlarmIntervalUnit()) {
                case 2:
                    interval *= 60;
                    break;
                case 3:
                    interval *= 3600;
                    break;
                case 4:
                    interval *= 86400;
                    break;
            }
            deployControl.setSamePersonAlarmInterval(interval);
            long redisExpire = Math.max(interval, 86400);
            List<Long> groupIds = groupIdMap.get(deployControlId);
            List<PeopleDeployControlFaceCompareVo> matchedFaces = new ArrayList<>();
            for (FaceDto faceDto : allFaceDtos) {
                DtoResult<MatchResult[]> matchResult = basicPeopleFaceFeatureService.faceComparisons(
                        groupIds.toArray(new Long[0]), faceDto, 100000 * groupIds.size());
                if (!matchResult.success() || matchResult.getData().length == 0) {
                    continue;
                }
                List<PeopleDeployControlFaceCompareVo> validMatches = Arrays.stream(matchResult.getData())
                        .map(m -> PeopleDeployControlFaceCompareVo.builder()
                                .peopleId(m.faceDto.getPeopleId())
                                .faceId(m.faceDto.getFaceId())
                                .similarity(m.getSimilarity())
                                .faceDto(faceDto)
                                .build())
                        .filter(vo -> vo.getSimilarity() >= Double.parseDouble(deployControl.getFaceSimilarity()))
                        .sorted(Comparator.comparing(PeopleDeployControlFaceCompareVo::getSimilarity).reversed())
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(validMatches)) {
                    PeopleDeployControlFaceCompareVo top = validMatches.get(0);
                    PeopleRelateVo relate = basicPeopleInfoService.selectPeopleRelateInfo(top.getPeopleId(), top.getFaceId());
                    if (ObjectUtil.isNotNull(relate)) {
                        top.setAppId(relate.getAppId());
                        top.setName(relate.getName());
                        top.setSex(relate.getSex());
                        top.setPeopleId(relate.getId());
                        top.setPeopleNumber(relate.getNumber());
                        top.setPhotoUrl(relate.getPhotoUrl());
                    }
                    matchedFaces.add(top);
                }
            }
            if (CollectionUtil.isEmpty(matchedFaces)) {
                log.info("V-LINKER算法平台.人脸识别告警, 源告警id:{}, 无有效匹配结果，布控任务id={}", alarmEntity.getId(), deployControlId);
                continue;
            }
            // 同人告警间隔校验
            List<PeopleDeployControlFaceCompareVo> filteredFaces = matchedFaces.stream()
                    .filter(vo -> {
                        Object cache = redisTemplate.opsForValue().get(RedisConstants.CONV_PEOPLE_DEPLOY_CONTROL_SAME_PERSON_ALARM_INTERVAL + vo.getPeopleNumber());
                        if (cache != null) {
                            LocalDateTime redisTime = LocalDateTimeUtil.parse(cache.toString(), "yyyy-MM-dd HH:mm:ss");
                            LocalDateTime currentTime = LocalDateTimeUtil.parse(alarmEntity.getAlarmTime(), "yyyy-MM-dd HH:mm:ss");
                            return currentTime.isAfter(redisTime.plusSeconds(deployControl.getSamePersonAlarmInterval()));
                        }
                        return true;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(filteredFaces)) {
                log.info("V-LINKER算法平台.人脸识别告警, 源告警id:{}, 所有人脸在同人告警间隔内，布控任务id={}", alarmEntity.getId(), deployControlId);
                continue;
            }
            alarmEntity.setPeopleDeployId(deployControlId);
            for (PeopleDeployControlFaceCompareVo vo : filteredFaces) {
                FaceDto faceDto = vo.getFaceDto();
                int key = faceDto.hashCode();
                if (!croppedFacePhotoUrlMap.containsKey(key)) {
                    String photoUrl = tailorFace(alarmEntity, faceDto, dateFormat);
                    if (photoUrl == null) {
                        continue;
                    }
                    faceDto.setPhotoUrl(photoUrl);
                    croppedFacePhotoUrlMap.put(key, photoUrl);
                } else {
                    faceDto.setPhotoUrl(croppedFacePhotoUrlMap.get(key));
                }
                alarmNormalizationExt.addPeopleInfoDto(
                        AlarmNormalizationExt.PeopleInfoDto.builder()
                                .peopleNumber(vo.getPeopleNumber()).build());
                alarmPeopleControlCompareRecordMapper.insert(AlarmPeopleControlCompareRecord.builder()
                        .alarmId(alarmEntity.getId())
                        .appId(vo.getAppId())
                        .peopleId(vo.getPeopleId())
                        .name(vo.getName())
                        .sex(vo.getSex())
                        .peoplePhotoId(vo.getFaceId())
                        .captureImage(faceDto.getPhotoUrl())
                        .matchPeopleImage(vo.getPhotoUrl())
                        .similarity(String.valueOf(vo.getSimilarity()))
                        .build());
                redisTemplate.opsForValue().set(
                        RedisConstants.CONV_PEOPLE_DEPLOY_CONTROL_SAME_PERSON_ALARM_INTERVAL + vo.getPeopleNumber(),
                        alarmEntity.getAlarmTime(),
                        redisExpire,
                        TimeUnit.SECONDS
                );
            }
            log.info("V-LINKER算法平台.人脸识别告警, 源告警id:{}, 布控任务id={}, 匹配成功数量:{}", alarmEntity.getId(), deployControlId, filteredFaces.size());
            hasAlarm = true;
        }
        if (hasAlarm) {
            alarmEntity.setNormalizationExt(JSON.toJSONString(alarmNormalizationExt, SerializerFeature.SkipTransientField));
        }
        log.info("V-LINKER算法平台.接收第三方平台告警, 源告警id:{}, 归还扩展信息:{}", alarmEntity.getId(), JSON.toJSONString(message.getNormalizationExt()));
    }


    /**
     * 裁剪人脸图片
     */
    private String tailorFace(AlarmEntity alarmEntity, FaceDto faceDto, String dateFormat) {
        try {
            String base64Crop = ImageCropperUtil.cropImage(faceDto.getPhotoUrlBase64(), faceDto.getX(), faceDto.getY(), faceDto.getW(), faceDto.getH());
            String ossObjKey = "alarm/peopleDeployControl/"  + dateFormat + "/" + alarmEntity.getDeviceId() + "/"+ IdWorker.getId() + ".jpg";
            DtoResult<FileModel> uploadResult = fileService.uploadByte(Base64.getDecoder().decode(base64Crop), ossObjKey);
            log.info("V-LINKER算法平台.人脸识别告警.源告警id:{},裁剪人脸图片成功，上传图片到s3结果，uploadResult={}", alarmEntity.getId(), uploadResult.success());
            if (uploadResult.success()) {
                return uploadResult.getData().getUrl();
            }
        } catch (Exception e) {
            log.error("V-LINKER算法平台.人脸识别告警.源告警id:{},裁剪人脸图片失败，message={}", alarmEntity.getId(), e.getMessage(), e);
        }
        return null;
    }

    /**
     * 只是处理告警图片推送
     */
    public void callBackOnlyImg(CallBackMessage message) {
        log.info("V-LINKER算法平台.接收第三方平台图片告警..向第三方平台推送图片告警数据，程序开始...message={}", JSON.toJSON(message));
        if (message == null || message.getAlertType() == null) {
            return;
        }
        if (StringUtil.isEmpty(message.getMsgId())) {
            return;
        }
        PushAlarmDto pushAlarmDto = this.getPushAlarmDto(message);
        pushAlarmDto.setMsgType(2);
        log.info("V-LINKER算法平台.向第三方推送告警图片..推送告警数据...message={}, pushAlarmDto={}", JSON.toJSON(message), JSON.toJSON(pushAlarmDto));
        // 1.内部推送
        callBackFeign.feignPush(pushAlarmDto);

        List<ThirdPartyDeviceEntity> thirdPartyDeviceEntityList = thirdPartyDeviceService.list(new LambdaQueryWrapper<ThirdPartyDeviceEntity>()
                .eq(ThirdPartyDeviceEntity::getCameraId, message.getDeviceId())
                .like(ThirdPartyDeviceEntity::getSubscribe, message.getAlertType())
                .groupBy(ThirdPartyDeviceEntity::getThirdId)
        );
        if (CollectionUtil.isEmpty(thirdPartyDeviceEntityList)) {
            log.info("V-LINKER算法平台.接收第三方平台图片告警..向第三方平台推送图片告警数据，第三方设备为空，程序结束...message={}", JSON.toJSON(message));
            return;
        }
        Set<Long> thirdIdSet = thirdPartyDeviceEntityList.stream().map(ThirdPartyDeviceEntity::getThirdId).collect(Collectors.toSet());
        List<ThirdPartyEntity> thirdPartyEntityList = thirdPartyService.list(new LambdaQueryWrapper<ThirdPartyEntity>()
                .in(ThirdPartyEntity::getId, thirdIdSet)
                .eq(ThirdPartyEntity::getStatus, 1)
                .eq(ThirdPartyEntity::getFeignReq, 0)
                .eq(ThirdPartyEntity::getInnerSystem, 0)
        );
        if (CollectionUtil.isNotEmpty(thirdPartyEntityList)) {
            // 2.外部推送
            for (ThirdPartyEntity thirdPartyEntity : thirdPartyEntityList) {
                try {
                    log.info("V-LINKER算法平台.向第三方推送告警图片..推送告警数据...thirdPartyEntity={}", JSON.toJSON(thirdPartyEntity));
                    if (!LocalUrlUtil.validUrl(thirdPartyEntity.getPushUrl())) {
                        log.info("V-LINKER算法中台.向第三方推送告警图片..pushUrl不合法，程序continue...thirdPartyEntity={}", JSON.toJSON(thirdPartyEntity));
                        continue;
                    }
                    PushAlarmDto dto = new PushAlarmDto();
                    dto.setMsgType(2);
                    dto.setMsgReqNo(message.getMsgId());
                    dto.setSrcUrl(message.getSrcUrl());
                    log.info("V-LINKER算法中台.向第三方推送告警图片...thirdPartyEntity={}, data={}", JSON.toJSON(thirdPartyEntity), JSON.toJSON(dto));
                    callBackFeign.httpPush(thirdPartyEntity, dto);
                } catch (Exception e) {
                    log.info("V-LINKER算法中台.向第三方推送告警图片，错误，程序continue...msg={}", e.getMessage(), e);
                }
            }
        }
    }

    private PushAlarmDto getPushAlarmDto(CallBackMessage message) {
        PushAlarmDto pushAlarmDto = new PushAlarmDto();
        pushAlarmDto.setMsgReqNo(message.getMsgId());
        pushAlarmDto.setDeviceCode(message.getDeviceCode());
        pushAlarmDto.setChannelId(message.getChannelId());
        pushAlarmDto.setAlertType(message.getAlertType());
        pushAlarmDto.setAlertTypeName(message.getAlertTypeName());
        pushAlarmDto.setAlertTime(message.getCreateTime());
        pushAlarmDto.setAlertSource(message.getAlertSource());
        pushAlarmDto.setAlertSourceName(message.getAlertSourceName());
        pushAlarmDto.setOriginalSrcUrl(message.getOriginalSrcUrl());
        pushAlarmDto.setSrcUrl(message.getSrcUrl());
        pushAlarmDto.setMetadata(message.getExt());
        pushAlarmDto.setNormalizationExt(message.getNormalizationExt());
        pushAlarmDto.setOriginalAlarmStr(message.getOriginalAlarmStr());
        return pushAlarmDto;
    }

    private boolean needReview(CallBackMessage message, AlgAlgorithmReviewConfigEntity algAlgorithmReviewConfigEntity) {
        Long algId = message.getAlgId();
        String deviceId = message.getDeviceId();

        Integer enable = algAlgorithmReviewConfigEntity.getEnable();
        Integer allAlgorithm = algAlgorithmReviewConfigEntity.getAllAlgorithm();
        Integer allDevice = algAlgorithmReviewConfigEntity.getAllDevice();
        Set<String> algorithmList = CollectionUtil.isEmpty(algAlgorithmReviewConfigEntity.getAlgorithmList()) ? new HashSet<>() : algAlgorithmReviewConfigEntity.getAlgorithmList();
        Set<String> deviceList = CollectionUtil.isEmpty(algAlgorithmReviewConfigEntity.getDeviceList()) ? new HashSet<>() : algAlgorithmReviewConfigEntity.getDeviceList();

        if (!Objects.equals(enable, 1)) {
            return false;
        }
        if (Objects.equals(allAlgorithm, 1) && Objects.equals(allDevice, 1)) {
            return true;
        } else if (Objects.equals(allAlgorithm, 1) && Objects.equals(allDevice, 0)) {
            return CollectionUtil.isNotEmpty(deviceList) && deviceList.contains(deviceId);
        } else if (Objects.equals(allAlgorithm, 0) && Objects.equals(allDevice, 1)) {
            return CollectionUtil.isNotEmpty(algorithmList) && algorithmList.contains(String.valueOf(algId));
        } else if (Objects.equals(allAlgorithm, 0) && Objects.equals(allDevice, 0)) {
            return (CollectionUtil.isNotEmpty(algorithmList) && algorithmList.contains(String.valueOf(algId))) && (CollectionUtil.isNotEmpty(deviceList) && deviceList.contains(deviceId));
        }
        return false;
    }
}