package com.saida.services.system.ops.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BaseEntity;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.OpsPlatformEntity;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.converge.entity.VirtualOrganTreeEntity;
import com.saida.services.converge.qxNode.req.*;
import com.saida.services.converge.qxNode.resp.ConvergeCascadeListResp;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.enums.SharePlatformMessageType;
import com.saida.services.common.mq.message.SharePlatformMessage;
import com.saida.services.system.api.service.ApiPlatformService;
import com.saida.services.system.ops.dto.EnabledPlatformDto;
import com.saida.services.system.ops.enums.GbVersionEnum;
import com.saida.services.system.ops.enums.ManufacturerEnum;
import com.saida.services.system.ops.mapper.OpsPlatformMapper;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.ops.vo.OpsPlatformVo;
import com.saida.services.system.rocketMq.nodev1.VirtualOrganTreeProduce;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName OpsPlatformServiceImpl
 * @Desc
 * @Date 2024/10/18 14:05
 */
@Slf4j
@Service
public class OpsPlatformServiceImpl extends ServiceImpl<OpsPlatformMapper, OpsPlatformEntity> implements OpsPlatformService {
    @Resource
    private SignalNodeService signalNodeService;
    @Resource
    private ApiPlatformService apiPlatformService;
    @Resource
    private VirtualOrganTreeService virtualOrganTreeService;
    @Resource
    private VirtualOrganTreeProduce virtualOrganTreeProduce;
    @Resource
    private VirtualOrganDeviceRelativeService virtualOrganDeviceRelativeService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DtoResult<Void> add(OpsPlatformEntity entity) {
        SignalNodeEntity node = signalNodeService.getById(entity.getNodeId());
        if (Objects.isNull(node)) {
            return DtoResult.error("级联节点不存在");
        }

        if (Objects.nonNull(this.findByName(entity.getName()))) {
            return DtoResult.error("平台名称已存在，请重新输入");
        }

        if (Objects.nonNull(findBySipId(entity.getSipId()))) {
            return DtoResult.error("sipId已存在，请重新输入");
        }

        if (Objects.nonNull(entity.getVirtualOrgId()) && Objects.nonNull(findByVirtualOrgId(entity.getVirtualOrgId()))) {
            return DtoResult.error("当前组织已绑定平台，请勿重复绑定");
        }

        int insert = getBaseMapper().insert(entity);
        if (insert > 0) {
            if (entity.getType() == 1) {
                // 新增上级平台
                return addParentPlatform(entity, node);
            } else if (entity.getType() == 2) {
                // 新增下级平台
                return addSubPlatform(entity, node);
            }
        }
        return DtoResult.ok();
    }

    @Override
    public OpsPlatformEntity findByName(String name) {
        LambdaQueryWrapper<OpsPlatformEntity> query = Wrappers.lambdaQuery();
        query.eq(OpsPlatformEntity::getName, name);
        return getBaseMapper().selectOne(query);
    }

    @Override
    public OpsPlatformEntity findBySipId(String sipId) {
        LambdaQueryWrapper<OpsPlatformEntity> query = Wrappers.lambdaQuery();
        query.eq(OpsPlatformEntity::getSipId, sipId);
        return getBaseMapper().selectOne(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DtoResult<Void> edit(OpsPlatformEntity entity) {
        if (Objects.isNull(entity.getId())) {
            return DtoResult.error("id不能为空");
        }

        OpsPlatformEntity dbPlatform = getBaseMapper().selectById(entity.getId());
        if (Objects.isNull(dbPlatform)) {
            return DtoResult.error("平台不存在");
        }

        SignalNodeEntity node = signalNodeService.getById(entity.getNodeId());
        if (Objects.isNull(node)) {
            return DtoResult.error("级联节点不存在");
        }

        OpsPlatformEntity platform = this.findByName(entity.getName());
        if (Objects.nonNull(platform) && !platform.getId().equals(entity.getId())) {
            return DtoResult.error("平台名称已存在，请重新输入");
        }

        OpsPlatformEntity sip = findBySipId(entity.getSipId());
        if (Objects.nonNull(sip) && !sip.getId().equals(entity.getId())) {
            return DtoResult.error("sipId已存在，请重新输入");
        }

        if (Objects.nonNull(entity.getVirtualOrgId())) {
            OpsPlatformEntity virtualOrgId = findByVirtualOrgId(entity.getVirtualOrgId());
            if (Objects.nonNull(virtualOrgId) && !entity.getVirtualOrgId().equals(virtualOrgId.getVirtualOrgId())) {
                return DtoResult.error("当前组织已绑定平台，请勿重复绑定");
            }
        }

        int res = getBaseMapper().updateById(entity);
        if (res > 0) {
            if (entity.getType() == 1) {
                // 编辑上级平台
                return addParentPlatform(entity, node);
            } else if (entity.getType() == 2) {
                // 编辑下级平台
                return addSubPlatform(entity, node);
            }
        }
        return DtoResult.ok();
    }

    @Override
    public OpsPlatformEntity findByVirtualOrgId(Long virtualOrgId) {
        LambdaQueryWrapper<OpsPlatformEntity> query = Wrappers.lambdaQuery();
        query.eq(OpsPlatformEntity::getVirtualOrgId, virtualOrgId);
        return getBaseMapper().selectOne(query);
    }

    @Override
    public Result deleteById(Long id) {
        if (Objects.isNull(id)) {
            return Result.error("id不能为空");
        }

        OpsPlatformEntity entity = getBaseMapper().selectById(id);
        if (Objects.isNull(entity)) {
            return Result.ok();
        }

        int res = getBaseMapper().deleteById(id);
        if (res > 0) {
            // 删除设备和通道
            List<DeviceEntity> deviceList = deviceService.getListByDeviceCode(entity.getSipId());
            if (CollectionUtil.isNotEmpty(deviceList)) {
                deviceService.deleteByDeviceCode(entity.getSipId());
                // 删除通道
                opsDeviceChannelService.deleteByDeviceIds(deviceList.stream().map(DeviceEntity::getId).collect(Collectors.toList()));
            }


            SignalNodeEntity node = signalNodeService.getById(entity.getNodeId());
            if (entity.getType() == 1) {
                apiPlatformService.convergeCascadeDelete(node , ConvergeCascadeDeleteReq.builder().sip_id(entity.getSipId()).build());
                // 组织取消共享
                virtualOrganTreeService.updateChildShareStatus(entity.getVirtualOrgId());
                // 设备取消共享
                virtualOrganDeviceRelativeService.updateShareStatusByTopOrgId(entity.getVirtualOrgId());
            } else if (entity.getType() == 2) {
                apiPlatformService.convergePlatformDelete(node , ConvergeCascadeDeleteReq.builder().sip_id(entity.getSipId()).build());
            }
        }
        return Result.ok("删除成功");
    }

    @Override
    public Result getInfo(Long id) {
        return Result.ok(getBaseMapper().selectById(id));
    }

    @Override
    public Result listPage(BaseRequest request, OpsPlatformEntity param) {
        IPage<OpsPlatformVo> page = baseMapper.listPage(new Page<>(request.getPageNum(), request.getPageSize()), param);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Result.ok(page);
        }

        // 查询网络状态
//        List<Long> ids = page.getRecords().stream().map(BaseEntity::getId).collect(Collectors.toList());
//        List<ConvergeCascadeListResp> refreshResp = this.refresh(ids);
//        Map<String, ConvergeCascadeListResp> sipMap = refreshResp.stream().collect(Collectors.toMap(ConvergeCascadeListResp::getSip_id, Function.identity(), (key1, key2) -> key1));

        List<SignalNodeEntity> nodeList = signalNodeService.list(new LambdaQueryWrapper<SignalNodeEntity>().in(SignalNodeEntity::getId, page.getRecords().stream().map(OpsPlatformEntity::getNodeId).collect(Collectors.toSet())));
        Map<String, SignalNodeEntity> nodeMap = nodeList.stream().collect(Collectors.toMap(SignalNodeEntity::getId, Function.identity(), (key1, key2) -> key1));

        // 上级平台查询虚拟组织
        Map<Long, String> orgMap = new HashMap<>();
        if (Objects.nonNull(param.getType()) && param.getType() == 1) {
            List<VirtualOrganTreeEntity> orgList = virtualOrganTreeService.list(new LambdaQueryWrapper<VirtualOrganTreeEntity>().in(BaseEntity::getId, page.getRecords().stream().map(OpsPlatformEntity::getVirtualOrgId).collect(Collectors.toSet())));
            orgMap = orgList.stream().collect(Collectors.toMap(BaseEntity::getId, VirtualOrganTreeEntity::getName));
        }

        for (OpsPlatformVo it : page.getRecords()) {
            it.setNetworkProtocol(GbVersionEnum.getNameByType(it.getNetworkProtocol()));
            it.setManufacturer(ManufacturerEnum.getNameByCode(Integer.valueOf(it.getManufacturer())));
            it.setNodeName(nodeMap.containsKey(it.getNodeId()) ? nodeMap.get(it.getNodeId()).getName() : "");
            it.setVirtualOrgName(orgMap.getOrDefault(it.getVirtualOrgId(), ""));
        }
        return Result.ok(page);
    }

    @Override
    public Result getList(OpsPlatformEntity param) {
        LambdaQueryWrapper<OpsPlatformEntity> query = Wrappers.lambdaQuery();
        query.eq(Objects.nonNull(param.getType()), OpsPlatformEntity::getType, param.getType())
                .like(StringUtil.isNotEmpty(param.getName()), OpsPlatformEntity::getName, param.getName())
                .like(StringUtil.isNotEmpty(param.getSipId()), OpsPlatformEntity::getSipId, param.getSipId());
        return Result.ok(getBaseMapper().selectList(query));
    }

    @Override
    public Result getGbVersionTypeList() {
        GbVersionEnum[] values = GbVersionEnum.values();
        return Result.ok(Arrays.stream(values).map(t -> {
            CountDto dto = new CountDto();
            dto.setType(String.valueOf(t.getCode()));
            dto.setTypeName(t.getDes());
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public Result getManufacturerTypeList() {
        ManufacturerEnum[] values = ManufacturerEnum.values();
        return Result.ok(Arrays.stream(values).map(t -> {
            CountDto dto = new CountDto();
            dto.setType(String.valueOf(t.getCode()));
            dto.setTypeName(t.getDes());
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public List<ConvergeCascadeListResp> refresh(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<ConvergeCascadeListResp> res = new ArrayList<>();
        List<OpsPlatformEntity> entities = getBaseMapper().selectBatchIds(ids);
        for (OpsPlatformEntity entity : entities) {
            SignalNodeEntity node = signalNodeService.getById(entity.getNodeId());
            if (node == null || node.getVersion() == 2){
                continue;
            }
            if (entity.getType() == 1) {
                // 查询上级平台
                List<ConvergeCascadeListResp> resp = apiPlatformService.convergeCascadeList(node, ConvergeCascadeListReq.builder().sip_id(entity.getSipId()).build());
                if (!CollectionUtil.isEmpty(resp)) {
                    res.addAll(resp);
                }
            } else if (entity.getType() == 2) {
                List<ConvergeCascadeListResp> resp = apiPlatformService.convergePlatformList(node, ConvergeCascadeListReq.builder().device_id(entity.getSipId()).build());
                if (!CollectionUtil.isEmpty(resp)) {
                    res.addAll(resp);
                }
            }

        }
        return res;
    }

    @Override
    public DtoResult<Void> enableSubscribe(Long id, Integer enabled) {
        OpsPlatformEntity entity = getBaseMapper().selectById(id);
        if (Objects.isNull(entity)) {
            return DtoResult.error("平台不存在");
        }

        if (entity.getType() == 1) {
            return DtoResult.error("上级域无法操作");
        }

        if (enabled.equals(entity.getEnabledSubscribe())) {
            return DtoResult.ok();
        }

        SignalNodeEntity node = signalNodeService.getById(entity.getNodeId());
        DtoResult<Void> result = apiPlatformService.convergeDeviceSubscribe(node, ConvergeDeviceSubscribeReq.builder()
                .alarm(enabled == 1)
                .catalog(enabled == 1)
                .position(enabled == 1)
                .device_ids(Lists.newArrayList(entity.getSipId()))
                .build());

        if (!result.success()) {
            return result;
        }

        // 更新订阅状态
        OpsPlatformEntity platform = new OpsPlatformEntity();
        platform.setId(id);
        platform.setEnabledSubscribe(enabled);
        getBaseMapper().updateById(platform);
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> enabled(EnabledPlatformDto dto) {
        // 查询平台
        OpsPlatformEntity entity = getBaseMapper().selectById(dto.getId());
        if (Objects.isNull(entity)) {
            return DtoResult.ok();
        }

        // 查询级联节点
        SignalNodeEntity node = signalNodeService.getById(entity.getNodeId());

        entity.setEnabled(dto.getEnabled());
        getBaseMapper().updateById(entity);
        return apiPlatformService.enabled(node, entity.getSipId(), entity.getEnabled());
    }

    @Override
    public DtoResult<Void> push(Long id) {
        OpsPlatformEntity entity = getBaseMapper().selectById(id);
        if (Objects.isNull(entity)) {
            return DtoResult.error("平台不存在");
        }

        // 查询级联节点
        SignalNodeEntity node = signalNodeService.getById(entity.getNodeId());
        if (Objects.isNull(node)) {
            return DtoResult.error("平台节点不存在");
        }

        // 发送MQ消息
        virtualOrganTreeProduce.sendPlatformShareMessage(SharePlatformMessage.builder()
                        .nodeId(node.getId())
                        .messageType(SharePlatformMessageType.PUSH.getCode())
                        .data(SharePlatformMessage.SharePlatformData.builder()
                                .id(String.valueOf(entity.getVirtualOrgId()))
                                .sipId(entity.getSipId())
                                .build())
                .build());
        return DtoResult.ok();
    }

    private DtoResult<Void> addParentPlatform(OpsPlatformEntity platform, SignalNodeEntity node) {
        ConvergeCascadeModifyReq req = new ConvergeCascadeModifyReq();
        req.setName(platform.getName());
        req.setGbPlatform(platform.getManufacturer());
        req.setVirtualOrganId(Objects.nonNull(platform.getVirtualOrgId()) ? String.valueOf(platform.getVirtualOrgId()) : null);
        req.setSipIp(platform.getSipIp());
        req.setSipId(platform.getSipId());
        req.setSipDomain(platform.getSip());
        req.setSipPort(platform.getSipPort());
        req.setUsername(StringUtil.isNotEmpty(platform.getUsername()) ? platform.getUsername() : "");
        req.setPassword(StringUtil.isNotEmpty(platform.getPassword()) ? platform.getPassword() : "");
        req.setEnabled(Objects.isNull(platform.getEnabled()) || platform.getEnabled() == 1);
        req.setGbVersion(platform.getNetworkProtocol());
        req.setRegisterExpire(platform.getRegistrationCycle());
        req.setKeepAliveExpire(platform.getHeartBeat());
        req.setEnabledAuth(StringUtil.isNotEmpty(platform.getPassword()));
        req.setTransport(platform.getTransportProtocol());
        return apiPlatformService.convergeCascadeModify(node, req);
    }

    private DtoResult<Void> addSubPlatform(OpsPlatformEntity platform, SignalNodeEntity node) {
        return apiPlatformService.convergePlatformModify(node, ConvergePlatformModifyReq.builder()
                .name(platform.getName())
                .gbPlatform(platform.getManufacturer())
                .sipId(platform.getSipId())
                .sipDomain(platform.getSip())
                .enabledAuth(platform.getAuthEnable() == 1)
                .username(platform.getUsername())
                .password(platform.getPassword())
                .enabled(Objects.isNull(platform.getEnabled()) || platform.getEnabled() == 1)
                .gbVersion(platform.getNetworkProtocol())
                .sipIp(platform.getSipIp())
                .sipPort(platform.getSipPort())
//                .transport(TransportProtocolEnums.getType(platform.getTransportProtocol()).getQxCode())
                .enabledAuth(StringUtil.isNotEmpty(platform.getPassword()))
                .mediaTransport(platform.getFetchingMode())
                .username(StringUtil.isNotEmpty(platform.getUsername()) ? platform.getUsername() : "")
                .password(StringUtil.isNotEmpty(platform.getPassword()) ? platform.getPassword() : "")
                .build());
    }
}
