package com.saida.services.system.ops.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.converge.entity.OpsPlatformEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.ops.dto.EnabledPlatformDto;
import com.saida.services.system.ops.dto.OpsPlatformDto;
import com.saida.services.system.ops.service.OpsPlatformService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OpsPlatformController
 * @Desc
 * @Date 2024/10/18 14:05
 */
@RestController
@RequestMapping("ops/newPlatform")
public class OpsPlatformController {

    @Resource
    private OpsPlatformService opsPlatformService;

    /**
     * 新增平台
     */
    @PostMapping("/add")
    @LogOperation(type = LogOperationEnum.ADD, func = "新增平台", module = ModuleEnum.PLATFORM)
    public DtoResult<Void> add(@RequestBody @Valid OpsPlatformEntity entity) {
        return opsPlatformService.add(entity);
    }

    /**
     * 编辑平台
     */
    @PostMapping("/edit")
    @LogOperation(type = LogOperationEnum.EDIT, func = "编辑平台", module = ModuleEnum.PLATFORM)
    public DtoResult<Void> edit(@RequestBody @Valid OpsPlatformEntity entity) {
        return opsPlatformService.edit(entity);
    }

    /**
     * 删除平台
     */
    @PostMapping("/delete")
    @LogOperation(type = LogOperationEnum.DELETE, func = "删除平台", module = ModuleEnum.PLATFORM)
    public Result delete(@RequestBody OpsPlatformEntity param) {
        return opsPlatformService.deleteById(param.getId());
    }

    @GetMapping("/getInfo")
    @LogOperation(type = LogOperationEnum.QUERY, func = "查询平台信息", module = ModuleEnum.PLATFORM)
    public Result getInfo(Long id) {
        return opsPlatformService.getInfo(id);
    }

    @GetMapping("/listPage")
    @LogOperation(type = LogOperationEnum.QUERY, func = "分页查询平台列表", module = ModuleEnum.PLATFORM)
    public Result listPage(BaseRequest request, OpsPlatformEntity param) {
        return opsPlatformService.listPage(request, param);
    }

    @GetMapping("/getList")
    @LogOperation(type = LogOperationEnum.QUERY, func = "查询平台列表", module = ModuleEnum.PLATFORM)
    public Result getList(OpsPlatformEntity param) {
        return opsPlatformService.getList(param);
    }

    /**
     * 刷新网络状态
     */
    @PostMapping("/refresh")
    @LogOperation(type = LogOperationEnum.EDIT, func = "刷新网络状态", module = ModuleEnum.PLATFORM)
    public Result refresh(@RequestBody OpsPlatformDto param) {
        return Result.ok(opsPlatformService.refresh(param.getIdList()));
    }

    /**
     * 获取联网协议类型
     */
    @GetMapping("/getGbVersionTypeList")
    @LogOperation(type = LogOperationEnum.QUERY, func = "获取联网协议类型", module = ModuleEnum.PLATFORM)
    public Result getGbVersionTypeList() {
        return opsPlatformService.getGbVersionTypeList();
    }

    /**
     * 获取平台厂家类型
     */
    @GetMapping("/getManufacturerTypeList")
    @LogOperation(type = LogOperationEnum.QUERY, func = "获取平台厂家类型", module = ModuleEnum.PLATFORM)
    public Result getManufacturerTypeList() {
        return opsPlatformService.getManufacturerTypeList();
    }

    /**
     * 开启关闭订阅
     */
    @PostMapping("/enableSubscribe")
    @LogOperation(type = LogOperationEnum.EDIT, func = "开启关闭订阅", module = ModuleEnum.PLATFORM)
    public DtoResult<Void> enableSubscribe(@RequestBody OpsPlatformEntity param) {
        return opsPlatformService.enableSubscribe(param.getId(), param.getEnabledSubscribe());
    }

    /**
     * 启禁用平台
     */
    @PostMapping("/enabled")
    @LogOperation(type = LogOperationEnum.EDIT, func = "启禁用平台", module = ModuleEnum.PLATFORM)
    public DtoResult<Void> enabled(@RequestBody EnabledPlatformDto dto) {
        return opsPlatformService.enabled(dto);
    }

    /**
     * 资源 推送
     */
    @PostMapping("/push")
    @LogOperation(type = LogOperationEnum.EDIT, func = "资源推送", module = ModuleEnum.PLATFORM)
    public DtoResult<Void> push(@RequestBody OpsPlatformDto dto) {
        return opsPlatformService.push(dto.getId());
    }
}
