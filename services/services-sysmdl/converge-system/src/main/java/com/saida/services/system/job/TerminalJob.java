package com.saida.services.system.job;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.enums.TerminalBoxTypeEnum;
import com.saida.services.system.algorithm.service.AlgorithmMappingService;
import com.saida.services.system.device.biz.BizAuthDto;
import com.saida.services.system.device.biz.vclusters.VclustersBiz;
import com.saida.services.system.device.biz.vclusters.req.VclustersTaskReq;
import com.saida.services.system.device.biz.vclusters.resp.VclustersTaskResp;
import com.saida.services.system.device.entity.BoxTaskEntity;
import com.saida.services.system.device.entity.CameraEntity;
import com.saida.services.system.device.entity.TerminalBoxEntity;
import com.saida.services.system.device.entity.TerminalBoxTaskDto;
import com.saida.services.system.device.service.BoxTaskService;
import com.saida.services.system.device.service.CameraService;
import com.saida.services.system.device.service.TerminalBoxService;
import com.saida.services.system.mqtt.pojo.AlgoTaskFind;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.VlinkerXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class TerminalJob {

    @Autowired
    private TerminalBoxService terminalBoxService;
    @Autowired
    private CameraService cameraService;
    @Autowired
    private BoxTaskService boxTaskService;
    @Resource
    private AlgorithmMappingService algorithmMappingService;
    @Autowired
    private VclustersBiz vclustersBiz;

    /**
     * 定时统计边缘盒子任务。
     * 该方法负责从边缘盒子获取设备任务信息，并进行相应的处理。
     * 根据盒子的类型（INTERLINGDA或RUICHI），采用不同的方式获取任务信息。
     * 对于INTERLINGDA类型，通过同步设备信息并执行算法任务查找来获取任务。
     * 对于RUICHI类型，通过发送请求到相应的API来获取任务信息。
     * 获取到任务信息后，将这些信息转换为统一的格式，并进行后续处理。
     * 注解用于将此方法注册为一个XXL-JOB的任务。
     */
    @VlinkerXxlJob(
            value = "statisticsDeviceTask",
            cron = "0 0/2 * * * ?",
            desc = "执行分析任务批次定时任务开始"
    )
    public ReturnT<String> statisticsDeviceTask() {
        // 记录任务开始执行的日志
        log.info("开始执行： statisticsDeviceTask");

        // 获取所有边缘盒子的信息
        List<TerminalBoxEntity> boxList = terminalBoxService.list();

        // 如果没有边缘盒子，直接返回
        if (boxList == null || boxList.isEmpty()) {
            return ReturnT.SUCCESS;
        }

        // 遍历每个边缘盒子，根据类型处理相应的任务
        for (TerminalBoxEntity box : boxList) {
            // 同步设备信息
            terminalBoxService.syncDevice(box, 1705477296068825090L);

            // 根据盒子类型处理不同的任务获取逻辑
            if (Objects.equals(TerminalBoxTypeEnum.INTERLINGDA.getCode(), box.getThirdType())) {
                // 对于INTERLINGDA类型的盒子，执行算法任务查找并处理结果
                terminalBoxService.algoTaskFind(box, (e) -> {
                    // 记录任务查找结果的日志
                    log.info("statisticsDeviceTask-algoTaskFind {}", JSON.toJSONString(e));
                    // 如果没有任务数据，直接返回
                    if (e.data == null) {
                        return;
                    }

                    // 将任务数据转换为统一的格式
                    List<AlgoTaskFind> taskFinds = (List<AlgoTaskFind>) e.data;
                    if (taskFinds.isEmpty()) {
                        return;
                    }

                    List<TerminalBoxTaskDto> boxTaskDtoList = new ArrayList<>();
                    taskFinds.forEach(taskFind -> {
                        taskFind.getTask().forEach(task -> {
                            TerminalBoxTaskDto boxTaskDto = new TerminalBoxTaskDto();
                            boxTaskDto.setType(task.getType());
                            boxTaskDto.setDeviceNo(taskFind.getDevInfo().getDeviceNo());
                            boxTaskDto.setAlgName(task.getAlgName());
                            boxTaskDto.setStatus(task.getStatus());
                            boxTaskDto.setPollStatus(task.getPollStatus());
                            boxTaskDtoList.add(boxTaskDto);
                        });
                    });

                    // 处理转换后的任务数据
                    extracted(String.valueOf(box.getModel()), box, boxTaskDtoList);
                });
            } else if (Objects.equals(TerminalBoxTypeEnum.RUICHI.getCode(), box.getThirdType())) {
                // 对于RUICHI类型的盒子，通过发送请求获取任务信息
                BizAuthDto bizAuthDto = new BizAuthDto();
                bizAuthDto.setUsername(box.getUsername());
                bizAuthDto.setPassword(box.getPassword());
                bizAuthDto.setThirdUrl(box.getThirdUrl());

                List<VclustersTaskResp.Task> allData = new ArrayList<>();
                int pageNum = 0;
                boolean hasNext = true;

                // 分页获取所有任务信息
                while (hasNext) {
                    VclustersTaskReq req = new VclustersTaskReq();
                    req.setStart(pageNum * 50);
                    req.setLimit(50);
                    VclustersTaskResp taskResp = vclustersBiz.tasks(req, bizAuthDto);
                    List<VclustersTaskResp.Task> tasks = taskResp.getTasks();
                    // 过滤出有有效身份信息的任务
//                    List<VclustersTaskResp.Task> collect = tasks.stream()
//                            .filter(e ->StringUtil.isNotEmpty(e.getIdentity()))
//                            .collect(Collectors.toList());
                    allData.addAll(tasks);
                    // 判断是否还有下一页
                    if (taskResp.getTotal() <= pageNum * 50) {
                        pageNum++;
                    } else {
                        hasNext = false;
                    }
                }
                // 将任务信息转换为统一的格式
                List<TerminalBoxTaskDto> boxTaskDtoList = new ArrayList<>();
                allData.forEach(task -> {
                    task.getRules().forEach(rule -> {
                        TerminalBoxTaskDto boxTaskDto = new TerminalBoxTaskDto();
                        boxTaskDto.setType(task.getAlg() + "-" + rule.getType());
                        boxTaskDto.setDeviceNo(String.valueOf(task.getCameraid()));
                        boxTaskDto.setAlgName(rule.getName());
                        boxTaskDto.setStatus(rule.getStatus() ? "正常" : "异常");
                        boxTaskDto.setPollStatus("");
                        boxTaskDtoList.add(boxTaskDto);
                    });
                });
                // 处理转换后的任务数据
                extracted(TerminalBoxTypeEnum.RUICHI.getLabel(), box, boxTaskDtoList);
            }
        }
        // 记录任务执行结束的日志
        log.info("结束执行： statisticsDeviceTask");
        return ReturnT.SUCCESS;
    }


    /**
     * 提取终端盒的任务信息，并根据相应的算法映射关系，创建或更新终端盒的任务实体。
     * 此方法首先从终端盒任务DTO列表中提取任务信息，并根据设备编号查询对应的摄像头实体。
     * 接着，根据盒子型号和任务类型查询算法映射实体，以确定任务应使用的算法ID。
     * 如果摄像头或算法映射不存在，则跳过当前任务。
     * 最后，删除原有的终端盒任务实体，并批量保存新的任务实体。
     *
     * @param box 终端盒实体，用于获取盒子ID和型号。
     * @param boxTaskDtoList 终端盒任务DTO列表，包含待处理的任务信息。
     */
    private void extracted(String sourceId, TerminalBoxEntity box, List<TerminalBoxTaskDto> boxTaskDtoList) {
        // 初始化任务实体列表
        List<BoxTaskEntity> boxTaskEntityList = new ArrayList<>();

        // 遍历DTO列表，为每个任务创建任务实体
        for (TerminalBoxTaskDto task : boxTaskDtoList) {
            // 根据设备编号查询摄像头实体
            CameraEntity c = cameraService.getOne(new LambdaQueryWrapper<CameraEntity>()
                    .eq(CameraEntity::getBoxDeviceCode, task.getDeviceNo()), false);
            // 如果摄像头不存在，则跳过当前任务
            if (c == null) {
                continue;
            }

            // 根据盒子型号和任务类型查询算法映射实体
            AlgorithmMappingEntity algorithmMappingEntity = algorithmMappingService.getOne(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                    .eq(AlgorithmMappingEntity::getSourceId, sourceId)
                    .eq(AlgorithmMappingEntity::getCode, task.getType()), false);
            // 如果算法映射不存在，则记录日志并跳过当前任务
            if (null == algorithmMappingEntity) {
                log.info("AI盒子算法任务查询...对应的盒子算法不存在");
                continue;
            }

            // 创建新的任务实体，并设置相关属性
            boxTaskEntityList.add(new BoxTaskEntity() {{
                setBoxId(box.getId());
                setDeviceId(c.getId());
                setAlgId(algorithmMappingEntity.getAlgorithmId());
            }});
        }

        // 删除原有的终端盒任务实体
        boxTaskService.remove(new LambdaQueryWrapper<BoxTaskEntity>().eq(BoxTaskEntity::getBoxId, box.getId()));
        // 如果有新的任务实体需要创建，则批量保存新的任务实体
        if (!boxTaskEntityList.isEmpty()) {
            boxTaskService.saveBatch(boxTaskEntityList);
        }
    }
}