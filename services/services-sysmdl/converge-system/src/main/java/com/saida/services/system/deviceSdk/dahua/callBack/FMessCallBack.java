package com.saida.services.system.deviceSdk.dahua.callBack;

import brave.Span;
import brave.Tracer;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.netsdk.lib.NetSDKLib;
import com.netsdk.lib.ToolKits;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.service.FileService;
import com.saida.services.converge.entity.OpsDeviceChannelEntity;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.deviceSdk.dahua.DaHuaInitMain;
import com.saida.services.system.deviceSdk.dahua.dto.DahuaSdkDto;
import com.saida.services.system.deviceSdk.dto.PtzDto;
import com.saida.services.system.ops.service.OpsDeviceAlarmService;
import com.saida.services.system.ops.service.OpsDeviceChannelService;
import com.saida.services.system.sys.dto.AlarmNotifyDto;
import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.NativeLong;
import com.sun.jna.Pointer;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.concurrent.*;

/**
 * 告警消息回调
 */
@Slf4j
@Component
public class FMessCallBack implements NetSDKLib.fMessCallBack {
    @Resource
    private Tracer tracer;

    @Override
    public boolean invoke(int lCommand, NetSDKLib.LLong lLoginID,
                          Pointer pStuEvent, int dwBufLen, String strDeviceIP,
                          NativeLong nDevicePort, Pointer dwUser) {
        // 如果没有当前的 Span（或者没有 traceId），则创建新的 Span
        Span newSpan = tracer.newTrace().name("FMessCallBack").start();
        try (Tracer.SpanInScope scope = tracer.withSpanInScope(newSpan)) {
            // ✅ traceId/span 自动注入 MDC（如果配置了日志桥）
            handleAlarm(lCommand, lLoginID, pStuEvent, dwBufLen, strDeviceIP, nDevicePort, dwUser);
        } catch (Exception e) {
            log.error("❌❌❌❌ FMessCallBack err：{}", e.getMessage());
        } finally {
            newSpan.finish();
        }
        return true;
    }


    @Resource
    private DaHuaInitMain daHuaInitMain;

    @Resource
    private OpsDeviceAlarmService opsDeviceAlarmService;

    @Resource
    private FileService fileService;

    private final Map<NetSDKLib.LLong, Set<Integer>> recentAlarms = new ConcurrentHashMap<>();

    private final BlockingQueue<Runnable> taskQueue = new ArrayBlockingQueue<>(100);

    private final ExecutorService scheduler = new ThreadPoolExecutor(
            5, 10, 60L, TimeUnit.SECONDS, taskQueue
    );

    public static void main(String[] args) {
        // 0x31d5 转int
        System.out.println(Integer.parseInt("31d5", 16));
    }

    /**
     *
     */
    private void handleAlarm(int lCommand, NetSDKLib.LLong lLoginID, Pointer pStuEvent, int dwBufLen,
                             String strDeviceIP, NativeLong nDevicePort, Pointer dwUser) {
        switch (lCommand) {
            // 31D5 烟 12757
            case 0x31d5: {
                // 火情告警
                // 检查是否在最近处理过的告警列表中
                Set<Integer> alarms = recentAlarms.computeIfAbsent(lLoginID, k -> new HashSet<>());
                if (!alarms.add(lCommand)) {
                    log.info("重复告警，忽略");
                    return;
                }
                try {
                    log.info("lCommand:{},lLoginID:{},pStuEvent:{},dwBufLen:{},strDeviceIP:{},nDevicePort:{},dwUser:{}"
                            , lCommand, lLoginID, pStuEvent, dwBufLen, strDeviceIP, nDevicePort, dwUser);
//                    byte[] alarm = new byte[dwBufLen];
//                    pStuEvent.read(0, alarm, 0, dwBufLen);

                    // 官方没处理 明确解不了
//                    String alarmFirewarningInfoDetail = new String(alarm, StandardCharsets.UTF_8);
//                    String jsonString = JSON.toJSONString(alarmFirewarningInfoDetail);
                    log.info("大华告警对象  NET_ALARM_FIREWARNING_INFO：alarm:{} jsonString:{}", "官方说解不了", "官方说解不了");
                    scheduler.submit(() -> {
                        try {
                            tongbuzhuatu(lCommand, lLoginID, new Object(), 20);
                        } catch (Exception e) {
                            log.info("NET_ALARM_FIREWARNING_INFO 同步抓图失败 走异步抓图！ lLoginID：{}", lLoginID);
                        } finally {
                            // ... 释放锁 ...
                            alarms.remove(lCommand);
                        }
                    });
                } catch (Exception e) {
                    log.error("handleAlarm 异常", e);
                } finally {
                    // ... 释放锁 ...
                    alarms.remove(lCommand);
                }
                break;
            }
            case NetSDKLib.NET_ALARM_FIREWARNING_INFO: {
                // 火情告警
                // 检查是否在最近处理过的告警列表中
                Set<Integer> alarms = recentAlarms.computeIfAbsent(lLoginID, k -> new HashSet<>());
                if (!alarms.add(lCommand)) {
                    log.info("重复告警，忽略");
                    return;
                }
                try {
                    log.info("lCommand:{},lLoginID:{},pStuEvent:{},dwBufLen:{},strDeviceIP:{},nDevicePort:{},dwUser:{}"
                            , lCommand, lLoginID, pStuEvent, dwBufLen, strDeviceIP, nDevicePort, dwUser);
                    byte[] alarm = new byte[dwBufLen];
                    pStuEvent.read(0, alarm, 0, dwBufLen);
                    NetSDKLib.ALARM_FIREWARNING_INFO_DETAIL alarmFirewarningInfoDetail = new NetSDKLib.ALARM_FIREWARNING_INFO_DETAIL();
                    ToolKits.GetPointerData(pStuEvent, alarmFirewarningInfoDetail);
                    String jsonString = JSON.toJSONString(alarmFirewarningInfoDetail);
                    log.info("大华告警对象  NET_ALARM_FIREWARNING_INFO：alarm len:{} dto:{}", jsonString.length(), jsonString);
                    scheduler.submit(() -> {
                        try {
                            tongbuzhuatu(lCommand, lLoginID, alarmFirewarningInfoDetail, 20);
                        } catch (Exception e) {
                            log.info("NET_ALARM_FIREWARNING_INFO 同步抓图失败 走异步抓图！ lLoginID：{}", lLoginID);
                        } finally {
                            // ... 释放锁 ...
                            alarms.remove(lCommand);
                        }
                    });
                } catch (Exception e) {
                    log.error("handleAlarm 异常", e);
                } finally {
                    // ... 释放锁 ...
                    alarms.remove(lCommand);
                }
                break;
            }
            case NetSDKLib.EVENT_IVS_CROSSREGIONDETECTION: {
                // 区域入侵
                // 检查是否在最近处理过的告警列表中
                Set<Integer> alarms = recentAlarms.computeIfAbsent(lLoginID, k -> new HashSet<>());
                if (!alarms.add(lCommand)) {
                    log.info("重复告警，忽略");
                    return;
                }

                try {
                    log.info("lCommand:{},lLoginID:{},pStuEvent:{},dwBufLen:{},strDeviceIP:{},nDevicePort:{},dwUser:{}"
                            , lCommand, lLoginID, pStuEvent, dwBufLen, strDeviceIP, nDevicePort, dwUser);
                    byte[] alarm = new byte[dwBufLen];
                    pStuEvent.read(0, alarm, 0, dwBufLen);
                    NetSDKLib.DEV_EVENT_CROSSREGION_INFO devEventCrossregionInfo = new NetSDKLib.DEV_EVENT_CROSSREGION_INFO();
                    ToolKits.GetPointerData(pStuEvent, devEventCrossregionInfo);
                    String jsonString = JSON.toJSONString(devEventCrossregionInfo);
                    log.info("大华告警对象  EVENT_IVS_CROSSREGIONDETECTION ：alarm dto:{}", jsonString);
                    scheduler.submit(() -> {
                        try {
                            tongbuzhuatu(lCommand, lLoginID, devEventCrossregionInfo, 28);
                        } catch (Exception e) {
                            log.info("NET_ALARM_FIREWARNING_INFO 同步抓图失败 走异步抓图！ lLoginID：{}", lLoginID);
                        } finally {
                            // ... 释放锁 ...
                            alarms.remove(lCommand);
                        }
                    });
                } catch (Exception e) {
                    log.error("handleAlarm 异常", e);
                } finally {
                    // ... 释放锁 ...
                    alarms.remove(lCommand);
                }
                break;
            }
            default:
                break;

        }
    }

    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;

    private void tongbuzhuatu(int lCommand, NetSDKLib.LLong lLoginID, Object alarmFirewarningInfoDetail, Integer type) {
        log.info("tongbuzhuatu start lLoginID:{},alarmFirewarningInfoDetail:{},type:{}", lLoginID, alarmFirewarningInfoDetail, type);

        String dateFormat = DateUtil.format(new Date(), "yyyyMMdd");
        String timeFormat = DateUtil.format(new Date(), "HHmmss");

        DahuaSdkDto dtoByLong = daHuaInitMain.getDtoByLong(lLoginID.longValue());
        if (dtoByLong == null) {
            log.error("未找到设备id！  lLoginID.longValue:{}", lLoginID.longValue());
            throw new BizRuntimeException("未找到设备id");
        }
        NetSDKLib netsdk = DaHuaInitMain.getNetsdk();
        if (netsdk == null) {
            log.error("未找到设备netsdk！  lLoginID.longValue:{}", lLoginID.longValue());
            throw new BizRuntimeException("未找到设备netsdk");
        }
        String deviceCodeByLong = dtoByLong.getDeviceCode();
        if (deviceCodeByLong == null) {
            log.error("未找到设备deviceCodeByLong！  lLoginID.longValue:{}", lLoginID.longValue());
            throw new BizRuntimeException("未找到设备deviceCodeByLong");
        }

        SnapPictureToFile snapPictureToFile = snapPictureToFile(timeFormat, netsdk, lLoginID, 0);
        if (snapPictureToFile == null) {
            log.info("snapPictureToFile 大华抓图失败 ossKey1:");
            return;
        }
        String ossKey1 = upload(snapPictureToFile, deviceCodeByLong, dateFormat);
        log.info("snapPictureToFile 上传告警图片到s3 ossKey1：{}", ossKey1);

        SnapPictureToFile snapPictureToFile2 = snapPictureToFile(timeFormat, netsdk, lLoginID, 1);
        if (snapPictureToFile2 == null) {
            log.info("snapPictureToFile2 大华抓图失败 ossKey2:");
            return;
        }
        String ossKey2 = upload(snapPictureToFile2, deviceCodeByLong, dateFormat);
        log.info("snapPictureToFile2 上传告警图片到s3 ossKey2：{}", ossKey2);

        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceSn, deviceCodeByLong)
                .orderByDesc(OpsDeviceChannelEntity::getChannelName)
                .last(" limit 1 "));

        AlarmNotifyDto dto = new AlarmNotifyDto();
        dto.setId(UUID.randomUUID().toString());
        dto.setSn(deviceCodeByLong);
        dto.setMsg_id(dto.getId());
        dto.setPriority(1);
        dto.setMethod(5);
        dto.setTimestamp(System.currentTimeMillis());
        dto.setType(type);
        if (one != null) {
            dto.setChannel_id(one.getChannelId());
        }
        List<String> a = new ArrayList<>();
        a.add(ossKey1);
        a.add(ossKey2);
        dto.setSnap_paths(a);
        PtzDto ptzDto = daHuaInitMain.doGetPTZDto(lLoginID);
        if (ptzDto != null) {
            ptzDto.setNPTZPan(ptzDto.getNPTZPan() == null ? 0 : ptzDto.getNPTZPan() / 10);
            ptzDto.setNPTZTilt(ptzDto.getNPTZTilt() == null ? 0 : ptzDto.getNPTZTilt() / 10);
        }
        JSONObject ext = new JSONObject();
        ext.put("ptz", ptzDto);
        ext.put("alarm_info", alarmFirewarningInfoDetail);
        dto.setExt(ext);
        dto.setOriginalAlarmStr("dahua-sdk:" + lCommand);
        dto.setSdkAccess(true);

        log.info("大华sdk告警发送：dto:{}", JSON.toJSONString(dto));
        try {
            opsDeviceAlarmService.handleMessage(dto);
        } catch (Exception e) {
            log.error("大华sdk告警发送失败：dto:{}", JSON.toJSONString(dto), e);
        }
    }


    @Data
    @Builder
    public static class SnapPictureToFile {
        private String fileName;
        private byte[] buf;
    }

    private String upload(SnapPictureToFile snapPictureToFile, String deviceCodeByLong, String dateFormat) {
        log.info("snapPictureToFile 上传告警图片到s3 buf：{}", snapPictureToFile.buf.length);
        ByteArrayInputStream byteArrInput = new ByteArrayInputStream(snapPictureToFile.buf);
        String ossObjKey = "alarm/" +  dateFormat + "/" +deviceCodeByLong + "/" + IdWorker.getId() + ".jpg";
        DtoResult<FileModel> fileModelDtoResult = fileService.uploadToS3(byteArrInput, ossObjKey, (long) snapPictureToFile.buf.length, "alarm");
        //删除这个告警图片
        boolean del1 = FileUtil.del(snapPictureToFile.fileName);
        log.info("snapPictureToFile 删除告警图片结果 del1：{} , fileName:{}", del1, snapPictureToFile.fileName);
        if (!fileModelDtoResult.success()) {
            log.error("snapPictureToFile 上传告警图片到s3 失败 ossKey:{} msg:{} err:{}", ossObjKey, fileModelDtoResult.getMessage(), fileModelDtoResult.getError());
            return null;
        }
        return fileModelDtoResult.getData().getUrl();
    }

    private synchronized SnapPictureToFile snapPictureToFile(String timeFormat,
                                                             NetSDKLib netsdk, NetSDKLib.LLong lLoginID, int channel) {
        Pointer pInbuf = null;
        Pointer pOutbuf = null;
        Pointer pOutbuf2 = null;
        try {
            //同步抓图
            NetSDKLib.NET_IN_SNAP_PIC_TO_FILE_PARAM snapParamIn = new NetSDKLib.NET_IN_SNAP_PIC_TO_FILE_PARAM();
            snapParamIn.stuParam.Channel = channel;
            snapParamIn.stuParam.mode = 0;  // -1:表示停止抓图, 0：表示请求一帧, 1：表示定时发送请求, 2：表示连续请求
            snapParamIn.stuParam.Quality = 3; // 画质；1~6
            snapParamIn.stuParam.ImageSize = 1; // 0：QCIF,1：CIF,2：D1
            snapParamIn.stuParam.InterSnap = 5;
            snapParamIn.stuParam.CmdSerial = channel;
            NetSDKLib.NET_OUT_SNAP_PIC_TO_FILE_PARAM snapParamOut = new NetSDKLib.NET_OUT_SNAP_PIC_TO_FILE_PARAM();
            String fileName = "dahuaAlarm/SyncSnapPicture_" + timeFormat + "_" + IdWorker.getId() + ".jpg";
            System.arraycopy(fileName.getBytes(), 0, snapParamIn.szFilePath, 0, fileName.getBytes().length);
            int timeOut = 5000; // 5 second
            pInbuf = new Memory(snapParamIn.size());
            pInbuf.clear(snapParamIn.size());
            ToolKits.SetStructDataToPointer(snapParamIn, pInbuf, 0);
            snapParamOut.dwPicBufLen = 5 * 1024 * 1024;
            pOutbuf2 = new Memory(5 * 1024 * 1024);
            pOutbuf2.clear(5 * 1024 * 1024);
            snapParamOut.szPicBuf = pOutbuf2;
            pOutbuf = new Memory(snapParamOut.size());
            pOutbuf.clear(snapParamOut.size());
            ToolKits.SetStructDataToPointer(snapParamOut, pOutbuf, 0);
            boolean b = netsdk.CLIENT_SnapPictureToFile(lLoginID, pInbuf, pOutbuf, timeOut);

            if (!b) {
                log.error("snapPictureToFile 读取失败! b:{}sdk异常信息 :{}", b, DaHuaInitMain.getErrorInfo());
                throw new BizRuntimeException("同步抓图失败! 1");
            }

            log.info("snapPictureToFile 读取成功！b:{}, res:{}", b, JSON.toJSONString(snapParamOut));
            byte[] buf = snapParamOut.szPicBuf.getByteArray(0, snapParamOut.dwPicBufLen);
            int i = buf.length - 1;
            for (; i >= 0; i--) {
                if (buf[i] != 0) {
                    break;
                }
            }
            if (i <= 0) {
                log.error("snapPictureToFile 读取失败! i <= 0");
                return null;
            }
            byte[] resBuf = Arrays.copyOfRange(buf, 0, i + 1);

            return SnapPictureToFile.builder().fileName(fileName).buf(resBuf).build();
        } catch (Exception e) {
            log.error("snapPictureToFile 同步抓图失败! 2", e);
        } finally {
            if (pInbuf != null) {
                Native.free(Pointer.nativeValue(pInbuf));//清理内存
                Pointer.nativeValue(pInbuf, 0); //防止 gc 重复回收
            }
            if (pOutbuf != null) {
                Native.free(Pointer.nativeValue(pOutbuf));
                Pointer.nativeValue(pOutbuf, 0);
            }
            if (pOutbuf2 != null) {
                Native.free(Pointer.nativeValue(pOutbuf2));
                Pointer.nativeValue(pOutbuf2, 0);
            }
        }
        return null;
    }
}