package com.saida.services.system.ops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.converge.entity.OpsPlatformEntity;
import com.saida.services.converge.qxNode.resp.ConvergeCascadeListResp;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.ops.dto.EnabledPlatformDto;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OpsPlatformService
 * @Desc
 * @Date 2024/10/18 14:05
 */
public interface OpsPlatformService extends IService<OpsPlatformEntity> {
    DtoResult<Void> add(OpsPlatformEntity entity);

    OpsPlatformEntity findByName(String name);

    OpsPlatformEntity findBySipId(String sipId);

    DtoResult<Void> edit(OpsPlatformEntity entity);

    OpsPlatformEntity findByVirtualOrgId(Long virtualOrgId);

    Result deleteById(Long id);

    Result getInfo(Long id);

    Result listPage(BaseRequest request, OpsPlatformEntity param);

    Result getList(OpsPlatformEntity param);

    Result getGbVersionTypeList();

    Result getManufacturerTypeList();

    List<ConvergeCascadeListResp> refresh(List<Long> ids);

    DtoResult<Void> enableSubscribe(Long id, Integer enabled);

    DtoResult<Void> enabled(EnabledPlatformDto dto);

    DtoResult<Void> push(Long id);
}
