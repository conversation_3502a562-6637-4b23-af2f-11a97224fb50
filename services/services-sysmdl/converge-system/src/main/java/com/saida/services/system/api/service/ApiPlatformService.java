package com.saida.services.system.api.service;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.base.DtoResult;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.converge.qxNode.QxNodeApiEnum;
import com.saida.services.converge.qxNode.req.*;
import com.saida.services.converge.qxNode.resp.ConvergeCascadeListResp;
import com.saida.services.system.client.nodev1.QxNodeReqUtil;
import com.saida.services.system.client.nodev1.ResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ApiPlatformService
 * @Desc
 * @Date 2024/10/21 16:25
 */
@Slf4j
@Service
public class ApiPlatformService {

    @Resource
    private QxNodeReqUtil qxNodeReqUtil;

    public DtoResult<Void> convergeCascadeModify(SignalNodeEntity node, ConvergeCascadeModifyReq req) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_CASCADE_MODIFY, node, req);
        log.info("V-LINKER汇聚平台.级联新增修改...resp={}", JSON.toJSONString(responseDto));
        if (responseDto.getHttpCode() != 200) {
            throw new BizRuntimeException(responseDto.getMsg());
        }
        return DtoResult.ok();
    }

    public void convergeCascadeDelete(SignalNodeEntity node, ConvergeCascadeDeleteReq req) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_CASCADE_DELETE, node, req);
        log.info("V-LINKER汇聚平台.级联删除...resp={}", JSON.toJSONString(responseDto));
    }

    public void convergePlatformDelete(SignalNodeEntity node, ConvergeCascadeDeleteReq req) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_PLATFORM_DELETE, node, req);
        log.info("V-LINKER汇聚平台.下级平台删除...resp={}", JSON.toJSONString(responseDto));
    }

    public List<ConvergeCascadeListResp> convergeCascadeList(SignalNodeEntity node, ConvergeCascadeListReq req) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_CASCADE_LIST, node, req);
        if (responseDto.getHttpCode() != 200 || JSON.parseObject(responseDto.getRes()).getInteger("total") == 0) {
            log.error("V-LINKER汇聚平台.级联列表调用失败, resp=:{}", JSON.toJSONString(responseDto));
            return new ArrayList<>();
        }
        List<ConvergeCascadeListResp> res = JSON.parseObject(responseDto.getRes()).getJSONArray("list").toJavaList(ConvergeCascadeListResp.class);
        log.info("V-LINKER汇聚平台.级联列表...resp={}", JSON.toJSONString(res));
        return res;
    }

    public List<ConvergeCascadeListResp> convergePlatformList(SignalNodeEntity node, ConvergeCascadeListReq req) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_PLATFORM_LIST, node, req);
        if (responseDto.getHttpCode() != 200 || JSON.parseObject(responseDto.getRes()).getInteger("total") == 0) {
            log.error("V-LINKER汇聚平台.下级平台列表调用失败, resp=:{}", JSON.toJSONString(responseDto));
            return new ArrayList<>();
        }
        List<ConvergeCascadeListResp> res = JSON.parseObject(responseDto.getRes()).getJSONArray("list").toJavaList(ConvergeCascadeListResp.class);
        log.info("V-LINKER汇聚平台.下级平台列表...resp={}", JSON.toJSONString(res));
        return res;
    }

    public DtoResult<Void> convergeDeviceSubscribe(SignalNodeEntity node, ConvergeDeviceSubscribeReq req) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_DEVICE_SUBSCRIBE, node, req);
        log.info("V-LINKER汇聚平台.下级平台列表...resp={}", JSON.toJSONString(req));
        if (responseDto.getHttpCode() != 200) {
            throw new BizRuntimeException(responseDto.getMsg());
        }
        return DtoResult.ok();
    }

    public DtoResult<Void> convergePlatformModify(SignalNodeEntity node, ConvergePlatformModifyReq req) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_PLATFORM_MODIFY, node, req);
        if (responseDto.getHttpCode() != 200) {
            throw new BizRuntimeException(responseDto.getMsg());
        }
        return DtoResult.ok();
    }

    public DtoResult<Void> convergeCascadeShare(SignalNodeEntity node, ConvergeCascadeShareReq req) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_PLATFORM_MODIFY, node, req);
        if (responseDto.getHttpCode() != 200) {
            throw new BizRuntimeException(responseDto.getMsg());
        }
        return DtoResult.ok();
    }

    public DtoResult<Void> enabled(SignalNodeEntity node, String sipId, Integer enabled) {
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CONVERGE_PLATFORM_ENABLED, node, ConvergeCascadeEnabledReq.builder()
                        .sipId(sipId)
                        .enabled(enabled == 1)
                .build());
        if (responseDto.getHttpCode() != 200) {
            throw new BizRuntimeException(responseDto.getMsg());
        }
        return DtoResult.ok();
    }
}
