package com.saida.services.system.alarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.algorithm.entity.AlgorithmManageEntity;
import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.enums.AlgAlgorithmSourceEnum;
import com.saida.services.enums.TerminalBoxTypeEnum;
import com.saida.services.system.alarm.entity.AlarmEntity;
import com.saida.services.system.alarm.pojo.dto.AiBoxAlarmPushDto;
import com.saida.services.system.alarm.service.AiBoxAlarmReceiveService;
import com.saida.services.system.alarm.service.AlarmService;
import com.saida.services.system.alarm.vo.DoubleMaiAlarmDto;
import com.saida.services.system.alarm.vo.DoubleMaiAlarmV2DTO;
import com.saida.services.system.alarm.vo.SaidaAlarmDto;
import com.saida.services.system.algorithm.service.AlgorithmManageService;
import com.saida.services.system.algorithm.service.AlgorithmMappingService;
import com.saida.services.system.callback.CallBackInvoke;
import com.saida.services.system.callback.CallBackMessage;
import com.saida.services.system.device.entity.CameraEntity;
import com.saida.services.system.device.entity.TerminalBoxEntity;
import com.saida.services.system.device.service.CameraService;
import com.saida.services.system.device.service.TerminalBoxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class AiBoxAlarmReceiveServiceImpl implements AiBoxAlarmReceiveService {

    private final static Pattern pattern = Pattern.compile("人脸\\[(.*?)]");

    @Resource
    private CameraService cameraService;
    @Resource
    private FileService fileService;
    @Resource
    private TerminalBoxService terminalBoxService;
    @Resource
    private CallBackInvoke callBackInvoke;
    @Resource
    private AlarmService alarmService;
    @Resource
    private AlgorithmMappingService algorithmMappingService;
    @Resource
    private AlgorithmManageService algorithmManageService;

    public static ValueFilter pictureFilter = (obj, name, value) ->
            "pictureData".equals(name) && value != null
                    ? "base64[Len=" + ((String) value).length() + "]"
                    : value;

    /**
     * 英特灵达
     */
    @Override
    public DtoResult<Void> receive(MultipartFile picture, MultipartFile originPic, MultipartFile facePic, MultipartFile bodyPic, MultipartFile video, AiBoxAlarmPushDto dto) {
        log.info("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警...picture={}, originPic={}, video={}, data={}", picture, originPic, video, JSON.toJSON(dto));
        if (StringUtil.isEmpty(dto.getData())) {
            log.info("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..推送消息为空，接收推送结束...picture={}, originPic={}, video={}, data={}", picture, originPic, video, JSON.toJSON(dto));
            return DtoResult.ok();
        }
        if (null == picture) {
            log.info("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..告警图片为空..接收推送结束...picture={}, originPic={}, video={}, data={}", picture, originPic, video, JSON.toJSON(dto));
            return DtoResult.ok();
        }
        List<TerminalBoxEntity> terminalBoxEntityList = terminalBoxService.list(new LambdaQueryWrapper<TerminalBoxEntity>()
                .eq(TerminalBoxEntity::getThirdType, TerminalBoxTypeEnum.INTERLINGDA.getCode()));
        if (CollectionUtil.isEmpty(terminalBoxEntityList)) {
            log.error("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..AI盒子列表为空...程序结束");
            return DtoResult.ok();
        }
        List<AiBoxAlarmPushDto.TestBoxAlarmPushDataInfo> AiBoxAlarmPushDataInfoList = JSONArray.parseArray(dto.getData(), AiBoxAlarmPushDto.TestBoxAlarmPushDataInfo.class);

        if (CollectionUtil.isNotEmpty(AiBoxAlarmPushDataInfoList)) {
            for (AiBoxAlarmPushDto.TestBoxAlarmPushDataInfo aiBoxAlarmPushDataInfo : AiBoxAlarmPushDataInfoList) {
                Optional<TerminalBoxEntity> optional = terminalBoxEntityList.stream().filter(t1 -> Objects.equals(t1.getThirdCode(), aiBoxAlarmPushDataInfo.getRecogDeviceNo())).findFirst();
                if (optional.isEmpty()) {
                    log.error("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..AI盒子不存在，程序continue...recogDeviceNo={}", aiBoxAlarmPushDataInfo.getRecogDeviceNo());
                    continue;
                }

                TerminalBoxEntity terminalBoxEntity = optional.get();
                Long model = terminalBoxEntity.getModel();
                Integer alertType = aiBoxAlarmPushDataInfo.getAlertType();
                String deviceCode = aiBoxAlarmPushDataInfo.getDeviceCode();

                CameraEntity cameraEntity = cameraService.getOne(new LambdaQueryWrapper<CameraEntity>()
                        .eq(CameraEntity::getChannelId, deviceCode)
                        .last("LIMIT 1"), false);
                if (cameraEntity == null) {
                    cameraEntity = cameraService.getOne(new LambdaQueryWrapper<CameraEntity>()
                            .eq(CameraEntity::getThirdCode, deviceCode)
                            .last("LIMIT 1"), false);
                    if (cameraEntity == null) {
                        log.info("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..设备不存在...程序continue");
                        continue;
                    }
                }
                String cameraCode = cameraEntity.getThirdCode();
                String channelId = cameraEntity.getChannelId();

                List<AlgorithmMappingEntity> algorithmMappingEntityList = algorithmMappingService.list(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                        .eq(AlgorithmMappingEntity::getSourceId, model)
                        .eq(AlgorithmMappingEntity::getCode, String.valueOf(alertType)));
                if (CollectionUtil.isEmpty(algorithmMappingEntityList)) {
                    log.info("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..对应的盒子算法不存在...程序continue");
                    continue;
                }
                for (AlgorithmMappingEntity algorithmMappingEntity : algorithmMappingEntityList) {
                    AlgorithmManageEntity algorithmManageEntity = algorithmManageService.getById(algorithmMappingEntity.getAlgorithmId());
                    if (null == algorithmManageEntity) {
                        log.info("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..对应的平台算法不存在...程序continue");
                        continue;
                    }
                    if (algorithmManageEntity.getStatus() != 1) {
                        log.info("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..对应的平台算法未启用...程序continue");
                        continue;
                    }

                    AlarmEntity alarmEntity = new AlarmEntity();
                    Long alarmId = IdWorker.getId();
                    alarmEntity.setId(alarmId);
                    alarmEntity.setAlarmSource(AlgAlgorithmSourceEnum.TERMINAL_BOX.getDicId());
                    alarmEntity.setDeviceId(cameraEntity.getId());
                    alarmEntity.setAlgorithmId(algorithmManageEntity.getId());
                    alarmEntity.setTerminalBoxId(terminalBoxEntity.getId());
                    alarmEntity.setOriginalAlarmStr("intellindust-box:" + alertType);
                    alarmEntity.setAlarmTimeLong(System.currentTimeMillis());

                    // 上传文件
                    DtoResult<FileModel> fResult = fileService.uploadToS3(picture, "alarm/intellindust");
                    if (!fResult.success()) {
                        log.info("V-LINKER算法中台.HTTP接收英特灵达AI盒子告警..上传文件失败..程序continue...picture={}, originPic={}, video={}, data={}, msg={}", picture, originPic, video, JSON.toJSON(dto), fResult.getMessage());
                        continue;
                    }
                    alarmEntity.setAlarmImageUrl(fResult.getData().getUrl());

                    String captureTime = aiBoxAlarmPushDataInfo.getCaptureTime();
                    if (StringUtil.isNotEmpty(captureTime)) {
                        DateTime dateTime = DateUtil.parse(captureTime);
                        alarmEntity.setYear(captureTime.substring(0, 4));
                        alarmEntity.setMonth(captureTime.substring(0, 7));
                        alarmEntity.setDay(captureTime.substring(0, 10));
                        alarmEntity.setTime(captureTime.substring(11));
                        alarmEntity.setAlarmTime(captureTime);
                        alarmEntity.setAlarmTimeLong(dateTime.getTime());
                        alarmEntity.setHourInt(DateUtil.hour(dateTime, true));
                    }
                    alarmEntity.setExt(aiBoxAlarmPushDataInfo.getExtInfo());
                    alarmEntity.setRemark(String.format("%s（%s）", AlgAlgorithmSourceEnum.TERMINAL_BOX.getName(), terminalBoxEntity.getName()));
                    CallBackMessage callBackMessage = new CallBackMessage() {{
                        setAlarmId(alarmId);
                        setDeviceId(String.valueOf(alarmEntity.getDeviceId()));
                        setAlgId(alarmEntity.getAlgorithmId());
                        setDeviceCode(cameraCode);
                        setChannelId(channelId);
                        setAlertType(String.valueOf(algorithmManageEntity.getId()));
                        setCreateTime(aiBoxAlarmPushDataInfo.getCaptureTime());
                        setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.TERMINAL_BOX.getTag()));
                        setAlertSourceName(AlgAlgorithmSourceEnum.TERMINAL_BOX.getName());
                        setSrcUrl(alarmEntity.getAlarmImageUrl());
                        setExt(aiBoxAlarmPushDataInfo.getExtInfo());
                    }};

                    alarmEntity.setCallbackMessage(JSON.toJSONString(callBackMessage));
                    callBackInvoke.callBack(alarmEntity, callBackMessage, true, true);
                }
            }
        }
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> receiveDoubleMai(DoubleMaiAlarmDto alarmDto) {
        String picData = alarmDto.getPic_data();
        String thirdCode = alarmDto.getDesc();
        if (picData == null) {
            picData = "";
        }
        if (picData.isEmpty()) {
            log.error("双迈Box告警消息接收，picData为空，接收推送结束...device={}", thirdCode);
            return DtoResult.error("picData为空");
        }
        alarmDto.setPic_data(picData.length() + "");
        log.info("V-LINKER算法中台.HTTP接收双迈AI盒子告警..开始接收告警数据...data={}", JSON.toJSON(alarmDto));
        CameraEntity cameraEntity = cameraService.getOne(new LambdaQueryWrapper<CameraEntity>()
                .eq(CameraEntity::getChannelId, thirdCode), false);
        if (cameraEntity == null) {
            log.error("双迈Box告警消息接收，设备ID数据库中不存在，接收推送结束...thirdCode={}", thirdCode);
            return DtoResult.error("设备ID数据库中不存在");
        }
        AlgorithmMappingEntity algorithmMappingEntity = algorithmMappingService.getOne(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                .eq(AlgorithmMappingEntity::getSourceId, TerminalBoxTypeEnum.DOUBLE_MAI_HTTP.getLabel())
                .eq(AlgorithmMappingEntity::getCode, alarmDto.getGeid()), false);
        if (null == algorithmMappingEntity) {
            log.info("双迈Box告警消息接收...对应的盒子算法不存在 target:{}", alarmDto.getGeid());
            return DtoResult.error("对应的盒子算法不存在");
        }
        AlgorithmManageEntity algorithmManageEntity = algorithmManageService.getById(algorithmMappingEntity.getAlgorithmId());
        if (null == algorithmManageEntity) {
            log.info("双迈Box告警消息接收...对应的平台算法不存在 target:{},algorithmMappingEntity:{}", alarmDto.getGeid(), JSON.toJSONString(algorithmMappingEntity));
            return DtoResult.error("对应的平台算法不存在");
        }
        if (algorithmManageEntity.getStatus() != 1) {
            log.info("双迈Box告警消息接收...对应的平台算法未启用 target:{},algorithmMappingEntity:{}", alarmDto.getGeid(), JSON.toJSONString(algorithmMappingEntity));
            return DtoResult.error("对应的平台算法未启用");
        }
        // 告警时间
        long timestamp = alarmDto.getTimestamp() * 1000;

        // 上传文件
        DtoResult<FileModel> fileModelDtoResult = fileService.uploadBase64(picData, "jpg", "alarm/doubleMai");
        if (!fileModelDtoResult.success()) {
            log.error("双迈Box告警消息接收，上传文件失败，接收推送结束...device={}", thirdCode);
            return DtoResult.error("上传文件失败");
        }
        String imgUrl = fileModelDtoResult.getData().getUrl();

        AlarmEntity alarmEntity = new AlarmEntity();
        Long alarmId = IdWorker.getId();
        alarmEntity.setId(alarmId);
        alarmEntity.setAlarmSource(AlgAlgorithmSourceEnum.TERMINAL_BOX.getDicId());
        alarmEntity.setDeviceId(cameraEntity.getId());
        alarmEntity.setAlgorithmId(algorithmManageEntity.getId());
        alarmEntity.setOriginalAlarmStr("doubleMai-box:" + alarmDto.getGeid());
        alarmEntity.setAlarmTimeLong(System.currentTimeMillis());
        alarmEntity.setAlarmImageUrl(imgUrl);
        alarmEntity.setExt(JSON.toJSONString(alarmDto));
        AlarmNormalizationExt alarmNormalizationExt = new AlarmNormalizationExt();
        if (CollectionUtil.isNotEmpty(alarmDto.getNn_output()) && !alarmDto.getNn_output().isEmpty()) {
            alarmDto.getNn_output().forEach(nnOutputDto -> {
                if (StringUtil.isNotEmpty(nnOutputDto.getClass_name())) {
                    // 人脸[陌生人][0.00]
                    Matcher matcher = pattern.matcher(nnOutputDto.getClass_name());
                    if (matcher.find()) {
                        alarmNormalizationExt.addPeopleInfoDto(AlarmNormalizationExt.PeopleInfoDto
                                .builder()
                                .peopleNumber(matcher.group(1))
                                .build());
                    }
                }
            });
        }
        DateTime dateTime = DateUtil.date(timestamp);
        alarmEntity.setYear(String.valueOf(DateUtil.year(dateTime)));
        alarmEntity.setMonth(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_MONTH_PATTERN)));
        alarmEntity.setDay(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN)));
        alarmEntity.setTime(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_TIME_PATTERN)));
        alarmEntity.setAlarmTime(DateUtil.format(dateTime, DatePattern.NORM_DATETIME_PATTERN));
        alarmEntity.setAlarmTimeLong(dateTime.getTime());
        alarmEntity.setHourInt(DateUtil.hour(dateTime, true));
        alarmEntity.setRemark(String.format("%s（%s）", AlgAlgorithmSourceEnum.TERMINAL_BOX.getName(), cameraEntity.getName()));
        alarmEntity.setNormalizationExt(JSON.toJSONString(alarmNormalizationExt, SerializerFeature.SkipTransientField));
        CallBackMessage callBackMessage = new CallBackMessage() {{
            setAlarmId(alarmId);
            setDeviceId(String.valueOf(alarmEntity.getDeviceId()));
            setAlgId(alarmEntity.getAlgorithmId());
            setDeviceCode(cameraEntity.getThirdCode());
            setChannelId(cameraEntity.getChannelId());
            setAlertType(String.valueOf(algorithmManageEntity.getId()));
            setCreateTime(alarmEntity.getAlarmTime());
            setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.TERMINAL_BOX.getTag()));
            setSrcUrl(alarmEntity.getAlarmImageUrl());
            setExt(alarmEntity.getExt());
            setNormalizationExt(alarmNormalizationExt);
        }};
        alarmEntity.setCallbackMessage(JSON.toJSONString(callBackMessage));

        log.info("双迈Box告警消息接收，告警保存成功...alarmEntity={}", JSON.toJSON(alarmEntity));
        callBackInvoke.callBack(alarmEntity, callBackMessage, true, true);
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> receiveDoubleMaiV2(DoubleMaiAlarmV2DTO dto) {
        log.info("V-LINKER算法中台.HTTP接收【双迈V2】盒子告警数据...dto={}", JSON.toJSONString(dto, pictureFilter, SerializerFeature.WriteMapNullValue));

        DoubleMaiAlarmV2DTO.DataInfo dataInfo = dto.getData();
        String type = dto.getType();
        String deviceNo = dataInfo.getDeviceNo();

        TerminalBoxEntity terminalBoxEntity = terminalBoxService.getAny(new LambdaQueryWrapper<TerminalBoxEntity>()
                .eq(TerminalBoxEntity::getThirdType, TerminalBoxTypeEnum.DOUBLE_MAI_V2_HTTP.getCode())
                .eq(TerminalBoxEntity::getThirdCode, deviceNo)
        );
        if (Objects.isNull(terminalBoxEntity)) {
            log.warn("V-LINKER算法中台.HTTP接收【双迈V2】盒子告警..AI盒子不存在，程序结束...deviceNo={}", deviceNo);
            return DtoResult.ok();
        }

        String alertType = dataInfo.getAlertType();
        String sceneCode = dataInfo.getSceneCode();

        CameraEntity cameraEntity = cameraService.getAny(new LambdaQueryWrapper<CameraEntity>()
                .eq(CameraEntity::getChannelId, sceneCode)
                );
        if (cameraEntity == null) {
            cameraEntity = cameraService.getAny(new LambdaQueryWrapper<CameraEntity>()
                    .eq(CameraEntity::getThirdCode, sceneCode)
                    );
            if (cameraEntity == null) {
                log.warn("V-LINKER算法中台.HTTP接收【双迈V2】盒子告警..设备不存在...程序结束");
                return DtoResult.ok();
            }
        }
        String cameraCode = cameraEntity.getThirdCode();
        String channelId = cameraEntity.getChannelId();

        List<AlgorithmMappingEntity> algorithmMappingEntityList = algorithmMappingService.list(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                .eq(AlgorithmMappingEntity::getSourceId, TerminalBoxTypeEnum.DOUBLE_MAI_V2_HTTP.getLabel())
                .eq(AlgorithmMappingEntity::getCode, alertType));
        if (CollectionUtil.isEmpty(algorithmMappingEntityList)) {
            log.warn("V-LINKER算法中台.HTTP接收【双迈V2】盒子告警..对应的盒子算法不存在...程序结束");
            return DtoResult.ok();
        }
        for (AlgorithmMappingEntity algorithmMappingEntity : algorithmMappingEntityList) {
            AlgorithmManageEntity algorithmManageEntity = algorithmManageService.getById(algorithmMappingEntity.getAlgorithmId());
            if (null == algorithmManageEntity) {
                log.warn("V-LINKER算法中台.HTTP接收【双迈V2】盒子告警..对应的平台算法不存在...程序结束");
                continue;
            }
            if (algorithmManageEntity.getStatus() != 1) {
                log.warn("V-LINKER算法中台.HTTP接收【双迈V2】盒子告警..对应的平台算法未启用...程序结束");
                continue;
            }

            DateTime dateTime = DateUtil.parse(dataInfo.getCaptureTime(), DatePattern.NORM_DATETIME_PATTERN);

            AlarmEntity alarmEntity = new AlarmEntity();
            Long alarmId = IdWorker.getId();
            alarmEntity.setId(alarmId);
            alarmEntity.setAlarmSource(AlgAlgorithmSourceEnum.TERMINAL_BOX.getDicId());
            alarmEntity.setDeviceId(cameraEntity.getId());
            alarmEntity.setAlgorithmId(algorithmManageEntity.getId());
            alarmEntity.setTerminalBoxId(terminalBoxEntity.getId());
            alarmEntity.setOriginalAlarmStr("doubleMaiV2-box:" + alertType);
            alarmEntity.setAlarmTimeLong(dateTime.getTime());

            // 上传文件
            DtoResult<FileModel> fileModelDtoResult = fileService.uploadBase64(dataInfo.getPictureData(), "jpg", "alarm/doubleMai");
            if (!fileModelDtoResult.success()) {
                log.error("V-LINKER算法中台.HTTP接收【双迈V2】盒子告警，上传文件失败，程序结束...msg={}", fileModelDtoResult.getMessage());
                return DtoResult.ok();
            }
            String imgUrl = fileModelDtoResult.getData().getUrl();
            alarmEntity.setAlarmImageUrl(imgUrl);

            alarmEntity.setAlarmTime(dataInfo.getCaptureTime());

            alarmEntity.setYear(String.valueOf(DateUtil.year(dateTime)));
            alarmEntity.setMonth(DateUtil.format(dateTime, DatePattern.NORM_MONTH_PATTERN));
            alarmEntity.setDay(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN));
            alarmEntity.setTime(DateUtil.format(dateTime, DatePattern.NORM_TIME_PATTERN));
            alarmEntity.setAlarmTimeLong(dateTime.getTime());
            alarmEntity.setHourInt(DateUtil.hour(dateTime, true));

            alarmEntity.setExt(JSON.toJSONString(dataInfo.getExtInfo(), SerializerFeature.SkipTransientField));
            alarmEntity.setRemark(String.format("%s（%s）", AlgAlgorithmSourceEnum.TERMINAL_BOX.getName(), terminalBoxEntity.getName()));

            CallBackMessage callBackMessage = new CallBackMessage() {{
                setAlarmId(alarmId);
                setDeviceId(String.valueOf(alarmEntity.getDeviceId()));
                setAlgId(alarmEntity.getAlgorithmId());
                setDeviceCode(cameraCode);
                setChannelId(channelId);
                setAlertType(String.valueOf(algorithmManageEntity.getId()));
                setCreateTime(dataInfo.getCaptureTime());
                setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.TERMINAL_BOX.getTag()));
                setAlertSourceName(AlgAlgorithmSourceEnum.TERMINAL_BOX.getName());
                setSrcUrl(alarmEntity.getAlarmImageUrl());
                setExt(alarmEntity.getExt());
            }};

            alarmEntity.setCallbackMessage(JSON.toJSONString(callBackMessage));
            callBackInvoke.callBack(alarmEntity, callBackMessage, true, true);
        }
        return DtoResult.ok();
    }

    /**
     * 瑞驰告警数据接收
     */
    @Override
    public DtoResult<Void> receiveRc(JSONObject jsonObject) {
        if (jsonObject == null) {
            return DtoResult.error("参数为空");
        }
        JSONObject event = jsonObject.getJSONObject("event");
        if (event == null) {
            return DtoResult.error("event为空");
        }
        // 告警类型
        String target = event.getString("target");
        // 触发类型
        String trigger = event.getString("trigger");

        target = target + "-" + trigger;

        // 告警信息
        JSONObject info = jsonObject.getJSONObject("info");
        if (info == null) {
            log.error("瑞驰Box告警消息接收，info为空，接收推送结束...");
            return DtoResult.error("info为空");
        }
        JSONObject camera = info.getJSONObject("camera");
        if (camera == null) {
            log.error("瑞驰Box告警消息接收，camera为空，接收推送结束...device={}", camera);
            return DtoResult.error("info为空");
        }
        // 设备ID
        String sn = camera.getString("identity");
        CameraEntity cameraEntity = cameraService.getOne(new LambdaQueryWrapper<CameraEntity>()
                .eq(CameraEntity::getId, sn), false);
        if (cameraEntity == null) {
            cameraEntity = cameraService.getOne(new LambdaQueryWrapper<CameraEntity>()
                    .eq(CameraEntity::getThirdCode, sn), false);
            if (null == cameraEntity) {
                log.error("瑞驰Box告警消息接收，设备ID数据库中不存在，接收推送结束...device={}", camera);
                return DtoResult.error("设备不存在");
            }
        }

        AlgorithmMappingEntity algorithmMappingEntity = algorithmMappingService.getOne(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                .eq(AlgorithmMappingEntity::getSourceId, TerminalBoxTypeEnum.RUICHI.getLabel())
                .eq(AlgorithmMappingEntity::getCode, target), false);
        if (null == algorithmMappingEntity) {
            log.info("瑞驰Box告警消息接收...对应的盒子算法不存在 target:{}", target);
            return DtoResult.error("对应的盒子算法不存在");
        }
        AlgorithmManageEntity algorithmManageEntity = algorithmManageService.getById(algorithmMappingEntity.getAlgorithmId());
        if (null == algorithmManageEntity) {
            log.info("瑞驰Box告警消息接收...对应的平台算法不存在 target:{},algorithmMappingEntity:{}", target, JSON.toJSONString(algorithmMappingEntity));
            return DtoResult.error("对应的平台算法不存在");
        }
        if (algorithmManageEntity.getStatus() != 1) {
            log.info("瑞驰Box告警消息接收...对应的平台算法未启用 target:{},algorithmMappingEntity:{}", target, JSON.toJSONString(algorithmMappingEntity));
            return DtoResult.error("对应的平台算法未启用");
        }
        // 告警时间
        String systime = jsonObject.getString("systime");
        JSONObject filedata = info.getJSONObject("filedata");
        if (filedata == null) {
            log.error("瑞驰Box告警消息接收，filedata为空，接收推送结束...device={}", camera);
            return DtoResult.error("filedata为空");
        }

        JSONObject sceneimage = filedata.getJSONObject("sceneimage");
        if (sceneimage == null) {
            log.error("瑞驰Box告警消息接收，sceneimage为空，接收推送结束...device={}", camera);
            return DtoResult.error("sceneimage为空");
        }
        // 上传文件
        DtoResult<FileModel> fileModelDtoResult = fileService.uploadBase64(sceneimage.getString("data"), "jpg", "alarm/rc");
        if (!fileModelDtoResult.success()) {
            log.error("瑞驰Box告警消息接收，上传文件失败，接收推送结束...device={}", camera);
            return DtoResult.error("上传文件失败");
        }
        String imgUrl = fileModelDtoResult.getData().getUrl();

        AlarmEntity alarmEntity = new AlarmEntity();
        Long alarmId = IdWorker.getId();
        alarmEntity.setId(alarmId);
        alarmEntity.setAlarmSource(AlgAlgorithmSourceEnum.TERMINAL_BOX.getDicId());
        alarmEntity.setDeviceId(cameraEntity.getId());
        alarmEntity.setAlgorithmId(algorithmManageEntity.getId());
        alarmEntity.setOriginalAlarmStr("vclusters-box:" + target);
        alarmEntity.setAlarmTimeLong(System.currentTimeMillis());
        alarmEntity.setAlarmImageUrl(imgUrl);
        if (StringUtil.isNotEmpty(systime)) {
            DateTime dateTime = DateUtil.parse(systime);
            if (dateTime != null) {
                alarmEntity.setYear(String.valueOf(DateUtil.year(dateTime)));
                alarmEntity.setMonth(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_MONTH_PATTERN)));
                alarmEntity.setDay(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN)));
                alarmEntity.setTime(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_TIME_PATTERN)));
                alarmEntity.setAlarmTime(systime);
                alarmEntity.setAlarmTimeLong(dateTime.getTime());
                alarmEntity.setHourInt(DateUtil.hour(dateTime, true));
            }
        }
        alarmEntity.setRemark(String.format("%s（%s）", AlgAlgorithmSourceEnum.TERMINAL_BOX.getName(), cameraEntity.getName()));
        String thirdCode = cameraEntity.getThirdCode();
        CallBackMessage callBackMessage = new CallBackMessage() {{
            setAlarmId(alarmId);
            setDeviceId(String.valueOf(alarmEntity.getDeviceId()));
            setAlgId(alarmEntity.getAlgorithmId());
            setDeviceCode(thirdCode);
            setAlertType(String.valueOf(algorithmManageEntity.getId()));
            setCreateTime(systime);
            setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.TERMINAL_BOX.getTag()));
            setSrcUrl(alarmEntity.getAlarmImageUrl());
        }};
        alarmEntity.setCallbackMessage(JSON.toJSONString(callBackMessage));

        log.info("瑞驰Box告警消息接收，告警保存成功...alarmEntity={}", JSON.toJSONString(alarmEntity));
        callBackInvoke.callBack(alarmEntity, callBackMessage, true, true);
        return DtoResult.ok();
    }


    @Override
    public DtoResult<Void> receiveSaida(SaidaAlarmDto alarmDto) {
        String picData = alarmDto.getImage();
        String thirdCode = alarmDto.getCamera_sn();
        if (picData == null) {
            picData = "";
        }
        if (picData.isEmpty()) {
            log.error("赛达Box告警消息接收，picData为空，接收推送结束...device={}", thirdCode);
            return DtoResult.error("picData为空");
        }
        alarmDto.setImage(picData.length() + "");
        log.info("V-LINKER算法中台.HTTP接收赛达AI盒子告警..开始接收告警数据...data={}", JSON.toJSON(alarmDto));
        CameraEntity cameraEntity = cameraService.getOne(new LambdaQueryWrapper<CameraEntity>()
                .eq(CameraEntity::getChannelId, thirdCode), false);
        if (cameraEntity == null) {
            log.error("赛达Box告警消息接收，设备ID数据库中不存在，接收推送结束...thirdCode={}", thirdCode);
            return DtoResult.error("设备ID数据库中不存在[" + thirdCode + "]");
        }
        AlgorithmMappingEntity algorithmMappingEntity = algorithmMappingService.getOne(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                .eq(AlgorithmMappingEntity::getSourceId, TerminalBoxTypeEnum.SAIDA_HTTP.getLabel())
                .eq(AlgorithmMappingEntity::getCode, alarmDto.getEvent_type()), false);
        if (null == algorithmMappingEntity) {
            log.info("赛达Box告警消息接收...对应的盒子算法不存在 target:{}", alarmDto.getEvent_type());
            return DtoResult.error("对应的盒子算法不存在[" + alarmDto.getEvent_type() + "]");
        }
        AlgorithmManageEntity algorithmManageEntity = algorithmManageService.getById(algorithmMappingEntity.getAlgorithmId());
        if (null == algorithmManageEntity) {
            log.info("赛达Box告警消息接收...对应的平台算法不存在 target:{},algorithmMappingEntity:{}", alarmDto.getEvent_type(), JSON.toJSONString(algorithmMappingEntity));
            return DtoResult.error("对应的平台算法不存在");
        }
        if (algorithmManageEntity.getStatus() != 1) {
            log.info("赛达Box告警消息接收...对应的平台算法未启用 target:{},algorithmMappingEntity:{}", alarmDto.getEvent_type(), JSON.toJSONString(algorithmMappingEntity));
            return DtoResult.error("对应的平台算法未启用");
        }
        if (alarmDto.getCreate_time() == null) {
            alarmDto.setCreate_time(System.currentTimeMillis());
        } else {
            alarmDto.setCreate_time(alarmDto.getCreate_time() * 1000);
        }
        // 告警时间
        long timestamp = alarmDto.getCreate_time();

        // 上传文件
        DtoResult<FileModel> fileModelDtoResult = fileService.uploadBase64(picData, "jpg", "alarm/saidaBox");
        if (!fileModelDtoResult.success()) {
            log.error("赛达Box告警消息接收，上传文件失败，接收推送结束...device={}", thirdCode);
            return DtoResult.error("上传文件失败");
        }
        String imgUrl = fileModelDtoResult.getData().getUrl();
        AlarmEntity alarmEntity = new AlarmEntity();
        Long alarmId = IdWorker.getId();
        alarmEntity.setId(alarmId);
        alarmEntity.setAlarmSource(AlgAlgorithmSourceEnum.TERMINAL_BOX.getDicId());
        alarmEntity.setDeviceId(cameraEntity.getId());
        alarmEntity.setAlgorithmId(algorithmManageEntity.getId());
        alarmEntity.setOriginalAlarmStr("saida-box:" + alarmDto.getEvent_type());
        alarmEntity.setAlarmTimeLong(System.currentTimeMillis());
        alarmEntity.setAlarmImageUrl(imgUrl);
        alarmEntity.setExt(JSON.toJSONString(alarmDto));
        AlarmNormalizationExt alarmNormalizationExt = new AlarmNormalizationExt();
        DateTime dateTime = DateUtil.date(timestamp);
        alarmEntity.setYear(String.valueOf(DateUtil.year(dateTime)));
        alarmEntity.setMonth(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_MONTH_PATTERN)));
        alarmEntity.setDay(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN)));
        alarmEntity.setTime(String.valueOf(DateUtil.format(dateTime, DatePattern.NORM_TIME_PATTERN)));
        alarmEntity.setAlarmTime(DateUtil.format(dateTime, DatePattern.NORM_DATETIME_PATTERN));
        alarmEntity.setHourInt(DateUtil.hour(dateTime, true));
        alarmEntity.setAlarmTimeLong(dateTime.getTime());
        alarmEntity.setRemark(String.format("%s（%s）", AlgAlgorithmSourceEnum.TERMINAL_BOX.getName(), cameraEntity.getName()));
        alarmEntity.setNormalizationExt(JSON.toJSONString(alarmNormalizationExt, SerializerFeature.SkipTransientField));
        CallBackMessage callBackMessage = new CallBackMessage() {{
            setAlarmId(alarmId);
            setDeviceId(String.valueOf(alarmEntity.getDeviceId()));
            setAlgId(alarmEntity.getAlgorithmId());
            setDeviceCode(cameraEntity.getThirdCode());
            setChannelId(cameraEntity.getChannelId());
            setAlertType(String.valueOf(algorithmManageEntity.getId()));
            setCreateTime(alarmEntity.getAlarmTime());
            setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.TERMINAL_BOX.getTag()));
            setSrcUrl(alarmEntity.getAlarmImageUrl());
            setExt(alarmEntity.getExt());
            setNormalizationExt(alarmNormalizationExt);
        }};
        alarmEntity.setCallbackMessage(JSON.toJSONString(callBackMessage));

        log.info("赛达Box告警消息接收，告警保存成功...alarmEntity={}", JSON.toJSONString(alarmEntity));
        callBackInvoke.callBack(alarmEntity, callBackMessage, true, true);
        return DtoResult.ok();
    }
}