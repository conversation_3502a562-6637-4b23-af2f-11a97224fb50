project:
  name: V-LINKER-网关
  version: 1.0.0
  version-time: 2024-04-28
feign:
  client:
    config:
      default:
        logger-level: full
        connectTimeout: 30000
        readTimeout: 30000
server:
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100，调整为5000
    accept-count: 5000
    threads:
      # tomcat最大线程数，默认为200，调整为4000
      max: 4000
      # Tomcat启动初始化的线程数，默认值10，调整为200
      min-spare: 200
    # 最大连接数
    max-connections: 20000

spring:
  main:
    allow-circular-references: true
  cloud:
    gateway:
      httpclient:
        response-timeout: 1000000
        connect-timeout: 60000
        pool:
          max-connections: 20000
          max-idle-time: 100000
          acquire-timeout: 60000
      routes: # List类型
        - id: converge-system-dispath
          uri: lb://converge-system-server
          predicates:
            - Path=/system/dispath/**
          filters:
            - RewritePath=/system/dispath/(?<remaining>.*),/converge-system/dispath/${remaining}
        - id: algorithm-system-server
          uri: lb://converge-system-server
          predicates:
            - Path=/algorithm-system/**
          filters:
            - RewritePath=/algorithm-system/(?<remaining>.*), /converge-system/${remaining}
        - id: converge-system-server
          uri: lb://converge-system-server
          predicates:
            - Path=/converge-system/**
        - id: open-system-server
          uri: lb://open-system-server
          predicates:
            - Path=/open-system/**
        - id: srv-system-server
          uri: lb://srv-system-server
          predicates:
            - Path=/srv-system/**
        - id: iot-system-server
          uri: lb://iot-system-server
          predicates:
            - Path=/iot-system/**
        - id: tripartite-system-server
          uri: lb://converge-system-server
          predicates:
            - Path=/tripartite-system/**
          filters:
            - RewritePath=/tripartite-system/(?<remaining>.*), /converge-system/${remaining}
        - id: uav-system-server
          uri: lb://uav-system-server
          predicates:
            - Path=/uav-system/**
        - id: vlinker2-application
          uri: lb://vlinker2-application
          predicates:
            - Path=/vlinker2/**
          filters:
            - StripPrefix=1
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedHeaders: "*"
            allowCredentials: true
            allowedOriginPatterns: "*"
            allowedMethods:
              - GET
              - POST
              - DELETE
              - PUT
              - OPTION
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Origin Access-Control-Allow-Credentials RETAIN_FIRST

  sleuth:
    enabled: true
    reactor:
      instrumentation-type: decorate_on_each

sysconfig:
  enableEndecrypt: 1
  uploadPath: v_file/
  static-resources:
    '[/file/**]': file:${sysconfig.uploadPath}

logging:
  level:
    com.saida.services.gateway: debug
    com.alibaba.nacos.client.config.impl: WARN
    org.apache.rocketmq: OFF  # 关闭mq的日志 不然直接打到本地
  config: classpath:logback-spring.xml

#加解密过滤
# 2024年12月19日10:01:59  api在写法上不能以api 、 iotApi 开始  因为他们是开放平台、物联平台的三方接口
filter:
  url:
    #返回加密过滤地址
    response:
      # 一些常见的静态资源
      - /favicon.ico
      - /**/**.html
      - /**/**.js
      - /**/**.css
      - /**/**.png
      - /**/**.jpeg
      - /**/**.jpg
      - /**/**.pcm
      - /**/**.pcma
      # 量子加密
      - /csp/*
      # 版本
      - /v
      - /*-system/v
      # sse长连接
      - /*-system/**/sse/**
      # 导出导入文件
      - /**/excelExport**
      - /**/excelExportTemplate/**
      - /**/excelAllExportTemplate**
      - /**/downloadExport**
      - /**/downloadImportTemplate**
      - /**/excelWord**
      - /**/files/excelErrDown
      - /*-system/websocket/**
      - /*-system/common/file/view
      - /*-system/files/view/**
      - /*/api/**   # 能开平台ak sk 应用API
      - /*/iotApi/**   # 物联网 的开放api接口
      - /*/inner/**   # 内部应用 2024年12月19日10:00:46  应该没有这样的接口了
      - /*/alarmPush/**   #告警推送
      - /**/alarmReceive/**   #告警接收
      - /**/alarm/receive/**   #告警接收
      # 融合平台
      - /srv-system/vcpCapability/** #电信订单同步
      # 算法平台
      - /algorithm-system/alarm/statistics/excelAlarmAllTypeExportOut
      - /algorithm-system/drawImg/**
      - /algorithm-system/alarm/statistics/excelAlarmEventTypeExportOut
      - /algorithm-system/alarm/statistics/excelAlarmPointTypeExportOut
      # 汇聚平台
      - /*-system/dispath/v1/signalNode   #获取信令节点
      - /system/dispath/v1/signalNode   #获取信令节点
      - /converge-system/ops/video/sdk/callDeviceByVideo  # 调试通话接口
      - /converge-system/ops/video/sdk/getPlayUrl  # 调试通话接口
      - /converge-system/ops/video/sdk/getChannelRecordUrl  # 调试通话接口
      - /converge-system/saidaSdk/login/v1/signalNode
      - /converge-system/saidaSdk/login/v2/*
      - /converge-system/dispath/v1/alarmReporting # 私有协议告警接受
      - /converge-system/opsDeviceVcp/vcp/**    #南向卡片
      - /converge-system/opsDeviceVcp/getRtmpUrlTest
      - /converge-system/ops/signalnode/gbListPage #获取gb信令主节点
      - /converge-system/ops/virtualOrganTree/organTemplate
      - /converge-system/ops/virtualOrganTree/deviceTemplate
      - /converge-system/ops/virtualOrganTree/deviceExport
      - /converge-system/ops/virtualOrganTree/exportChildList
      - /converge-system/juLong/mqtt/auth  # 巨龙mqtt认证
      - /converge-system/ehome/alarm/receive  # ehome告警接收
      # 物联网
      - /iot-system/receiveIot/dx/waterAnalysisData  # 物联网德希水位数据接收
      - /iot-system/shangZhi/deviceData/accept  # 尚志设备数据接收
      - /iot-system/jwa/iot/notify  # 精卫安平台物联数据接收
      - /uav-system/**
      - /srv-system/pay/aliPayNotify
      - /srv-system/vehicleType/download
      - /srv-system/maodu/maodugate/receive
      # 融合平台(组件化)
      - /vlinker2/captcha/**
      # websocket
      - /vlinker2/ws/stomp/**
      - /vlinker2/app/ws
      # Excel文件导入导出
      - /vlinker2/**/excelTemplate**/**
      - /vlinker2/**/excelImport**/**
      - /vlinker2/**/excelExport**/**
      # 告警数据通知
      - /vlinker2/**/compVisualDevice/onlineStatusCallback
      - /vlinker2/**/compIotAlarm/alarmCallback
      - /vlinker2/**/compWebAlarmCamera/alarmCallback
      # 支付-支付宝异步通知
      - /vlinker2/pay/**/notify/alipay
      # 支付-微信支付通知/退款通知
      - /vlinker2/pay/**/notify/wxpay
      - /vlinker2/pay/**/notify/wxpay/refund

    #请求解密过滤地址
    request:
      # 一些常见的静态资源
      - /favicon.ico
      - /**/**.html
      - /**/**.js
      - /**/**.css
      - /**/**.png
      - /**/**.jpeg
      - /**/**.jpg
      - /**/**.pcm
      - /**/**.pcma
      # 量子加密
      - /csp/*
      # 版本
      - /v
      - /*-system/v
      # sse长连接
      - /*-system/**/sse/**
      - /**/excelWord**
      - /*-system/websocket/**
      - /*-system/common/file/view
      - /*-system/files/view/**
      - /*/api/**   # 能开平台ak sk 应用API
      - /*/iotApi/**   # 物联网 的开放api接口
      - /*/inner/**   # 内部应用 2024年12月19日10:00:46  应该没有这样的接口了
      - /*/alarmPush/**   #告警推送
      - /**/alarmReceive/**   #告警接收
      - /**/alarm/receive/**   #告警接收
      # 融合平台
      - /srv-system/vcpCapability/** #电信订单同步
      # 算法平台
      - /algorithm-system/drawImg/**
      # 汇聚平台
      - /*-system/dispath/v1/signalNode   #获取信令节点
      - /system/dispath/v1/signalNode   #获取信令节点
      - /converge-system/ops/video/sdk/callDeviceByVideo  # 调试通话接口
      - /converge-system/ops/video/sdk/getPlayUrl  # 调试通话接口
      - /converge-system/ops/video/sdk/getChannelRecordUrl  # 调试通话接口
      - /converge-system/saidaSdk/login/v1/signalNode
      - /converge-system/saidaSdk/login/v2/*
      - /converge-system/dispath/v1/alarmReporting # 私有协议告警接受
      - /converge-system/opsDeviceVcp/vcp/**    #南向卡片
      - /converge-system/opsDeviceVcp/getRtmpUrlTest
      - /converge-system/ops/signalnode/gbListPage #获取gb信令主节点
      - /converge-system/juLong/mqtt/auth  # 巨龙mqtt认证
      - /converge-system/ehome/alarm/receive  # ehome告警接收
      # 物联网
      - /iot-system/receiveIot/dx/waterAnalysisData  # 物联网德希水位数据接收
      - /iot-system/shangZhi/deviceData/accept  # 尚志设备数据接收
      - /iot-system/jwa/iot/notify  # 精卫安平台物联数据接收
      - /uav-system/**
      - /srv-system/pay/aliPayNotify
      - /srv-system/vehicleType/download
      - /srv-system/maodu/maodugate/receive
      - /srv-system/yj/test/**
      # 融合平台(组件化)
      - /vlinker2/captcha/**
      # websocket
      - /vlinker2/ws/stomp/**
      - /vlinker2/app/ws
      # 告警数据通知
      - /vlinker2/**/compVisualDevice/onlineStatusCallback
      - /vlinker2/**/compIotAlarm/alarmCallback
      - /vlinker2/**/compWebAlarmCamera/alarmCallback

    #过滤token认证地址
    ignoreUrls:
      # 一些常见的静态资源
      - /favicon.ico
      - /**/**.html
      - /**/**.js
      - /**/**.css
      - /**/**.png
      - /**/**.jpeg
      - /**/**.jpg
      - /**/**.pcm
      - /**/**.pcma
      - /csp/*
      - /v
      - /*-system/v
      - /file/**
      - /*-system/auth/oauth/refreshToken
      - /*-system/auth/token
      - /system/auth/token
      - /*-system/auth/tokenByPhone/**
      - /*-system/auth/getSmsCodeByChangePassword
      - /*-system/auth/checkSmsCodeByChangePassword
      - /*-system/auth/changePasswordByPhone
      - /*-system/auth/captchaImage
      - /*-system/auth/getSMSCode
      - /*-system/auth/tokenByCaptchaImage
      - /*-system/auth/tokenByCaptchaImageV2
      - /*-system/auth/getVerificationCode
      - /*-system/auth/verifyAccount
      - /*-system/auth/registerAccount
      - /*-system/files/upload
      - /*-system/files/minioUpload
      - /*-system/sysBasicData/findList
      - /*-system/sysBasicDataDetail/findList
      - /*-system/sys/appversion/getLastVersion
      - /*-system/wxOfficial/login
      - /*-system/files/view/**
      - /*-system/sys/auth/loginCode
      - /*/api/**   # 能开平台ak sk 应用API
      - /*/iotApi/**   # 物联网 的开放api接口
      - /*/inner/**   # 内部应用  2024年12月19日10:00:46  应该没有这样的接口了
      - /*-system/platform/introduction/info
      - /*-system/solution/list
      - /*-system/solution/info
      - /*-system/sys/capabilityrelease/getList
      - /*-system/sys/capabilityrelease/getInfo
      - /*-system/capabilityCategory/getList
      - /*-system/capabilityCategory/info
      - /*-system/sys/region/getList #获取行政区域信息
      - /*/alarmPush/**   #告警推送
      - /**/alarmReceive/**   #告警接收
      - /**/alarm/receive/**   #告警接收
      - /**/emqx/auth
      - /srv-system/maodu/maodugate/receive
      # 汇聚相关
      - /*-system/dispath/v1/signalNode   #获取信令节点
      - /system/dispath/v1/signalNode   #获取信令节点
      - /converge-system/ops/video/sdk/callDeviceByVideo  # 调试通话接口
      - /converge-system/ops/video/sdk/getPlayUrl  # 调试通话接口
      - /converge-system/ops/video/sdk/getChannelRecordUrl  # 调试通话接口
      - /converge-system/saidaSdk/login/v1/signalNode
      - /converge-system/saidaSdk/login/v2/*
      - /converge-system/dispath/v1/alarmReporting # 私有协议告警接受
      - /converge-system/opsDeviceVcp/vcp/**    #南向卡片
      - /converge-system/opsDeviceVcp/getRtmpUrlTest
      - /converge-system/ops/signalnode/gbListPage #获取gb信令主节点
      - /converge-system/juLong/mqtt/auth  # 巨龙mqtt认证
      - /converge-system/ehome/alarm/receive  # ehome告警接收
      - /**/**.html
      - /**/**.css.**
      - /**/vcpCapability/** #电信订单同步
      # 物联网
      - /iot-system/receiveIot/dx/waterAnalysisData  # 物联网德希水位数据接收
      - /iot-system/shangZhi/deviceData/accept  # 尚志设备数据接收
      - /iot-system/jwa/iot/notify  # 精卫安平台物联数据接收
      # 算法相关
      - /algorithm-system/drawImg/**
      - /**/vcpCapability/**
      - /**/auth/getSMSCodeByApp
      - /**/auth/registered
      - /**/auth/token/app
      - /**/tokenByPhone/app
      - /srv-system/pay/aliPayNotify
      - /srv-system/auth/token/yzh
      - /uav-system/**
      - /srv-system/vehicleType/download
      - /srv-system/auth/changePasswordByAccount
      - /srv-system/auth/closeWeakPwd
      # 融合平台(组件化) 不走网关鉴权
      - /vlinker2/**

management:
  health:
    refresh:
      enabled: false
  endpoint:
    pause:
      enabled: false
    refresh:
      enabled: false
    restart:
      enabled: false
    resume:
      enabled: false
    env:
      post:
        enabled: false

license:
 path: PROJECT_PATH/doc/license/license.key
