package com.saida.services.converge.qxNode;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpMethod;

/**
 * 信令接口
 * <a href="https://apifox.com/apidoc/shared-9b40ac9b-f48b-4297-b5c3-e647168c5401">api文档</a>
 */
@Getter
@AllArgsConstructor
public enum QxNodeApiEnum {
    //设备相关

    LIST_DEVICE("/converge/device/list", "信令服务-设备列表", HttpMethod.POST, 1),

    ADD_DEVICE("/converge/device/add", "信令服务-新增设备", HttpMethod.POST, 1),

    PUSH_DEVICE_IP("/converge/device/push/ip", "信令服务-修改设备网域", HttpMethod.POST, 1),

    DEL_DEVICE("/converge/device/delete", "信令服务-删除设备", HttpMethod.POST, 1),

    DEVICE_DIAGNOSTICS("/converge/explain/device", "信令服务-设备诊断", HttpMethod.POST, 1),

    VIDEO_DIAGNOSTICS("/converge/explain/channel", "信令服务-视频诊断", HttpMethod.POST, 1),

    UPDATE_TRANSPORT_PROTOCOL("/converge/device/transport", "信令服务-修改设备通信协议", HttpMethod.POST, 1),

    GET_DEVICE_BASIC("/converge/device/basic/get", "信令服务-设备基本信息获取", HttpMethod.POST, 1),

    UPDATE_DEVICE_BASIC("/converge/device/basic/update", "信令服务-设备基本信息获取", HttpMethod.POST, 1),

    DEVICE_UPGRADE("/converge/device/upgrade", "信令服务-设备升级", HttpMethod.POST, 1),

    DEVICE_REBOOT("/converge/device/reboot", "信令服务-设备重启", HttpMethod.POST, 1),

    GET_STREAM_URL("/converge/channel/live", "信令服务-获取通道播放流地址", HttpMethod.POST, 1),

    CHANNEL_STOP_LIVE("/converge/channel/live/stop", "信令服务-停止直播", HttpMethod.POST, 1),

    GET_CHANNELS("/converge/channel/list", "信令服务-获取设备通道列表", HttpMethod.POST, 1),

    CATALOG("/converge/device/catalog", "信令服务-触发强制刷新通道", HttpMethod.POST, 1),

    GET_DEVICE_CONFIG("/converge/gb2022/getdeviceconfig", "信令服务-查询设备配置", HttpMethod.POST, 1),

    UPDATE_DEVICE_CONFIG("/converge/gb2022/setdeviceconfig", "信令服务-更新设备配置", HttpMethod.POST, 1),

    GET_SD_CARD_INFO("/converge/gb2022/getsdcardinfo", "信令服务-查询存储卡信息", HttpMethod.POST, 1),

    FORMAT_SD_CARD("/converge/gb2022/formatsdcard", "信令服务-重置存储卡", HttpMethod.POST, 1),

    //PTZ精准控制

    GET_PTZ_PRECISE("/converge/gb2022/getptzprecise", "信令服务-PTZ精准控制查询", HttpMethod.POST, 1),

    CTRL_PTZ_PRECISE("/converge/gb2022/ctrlptzprecise", "信令服务-PTZ精准控制", HttpMethod.POST, 1),

    //巡航轨迹
    GET_CRUISE_TRACK_LIST("/converge/gb2022/getcruisetracklist", "信令服务-巡航轨迹列表", HttpMethod.POST, 1),

    CTRL_CRUISE_TRACK_LIST("/converge/gb2022/ctrlcruisetrack", "信令服务-设置巡航轨迹", HttpMethod.POST, 1),

    GET_CRUISE_TRACK("/converge/gb2022/getcruisetrack", "信令服务-查询巡航轨迹", HttpMethod.POST, 1),

    //报警订阅
    SUBSCRIBE_ALARM("/converge/gb2022/subscribealarm", "信令服务-报警订阅", HttpMethod.POST, 1),

    SUBSCRIBE_DEVICE("/converge/device/subscribe", "信令服务-设备订阅", HttpMethod.POST, 1),

    //录像相关

    GET_CHANNEL_RECORD_MONTHS("/converge/record/month", "信令服务-查询月录像", HttpMethod.POST, 1),

    GET_CHANNEL_RECORD_TIME_LINE("/converge/record/timeline", "信令服务-查询录像时间轴", HttpMethod.POST, 1),

    GET_CHANNEL_RECORD_URL("/converge/record/records", "信令服务-查询录像播放地址", HttpMethod.POST, 1),

    CONTROL_PLAYBACK("/converge/record/playback/control", "信令服务-录像回看控制", HttpMethod.POST, 1),

    STOP_PLAYBACK("/converge/record/playback/stop", "信令服务-停止录像回看", HttpMethod.POST, 1),

    DOWNLOAD_PLAYBACK("/converge/record/download", "信令服务-国标设备-录像下载", HttpMethod.POST, 1),

    STOP_DOWNLOAD_PLAYBACK("/converge/record/download/stop", "信令服务-国标设备-停止录像下载", HttpMethod.POST, 1),

    CHANNEL_RECORD_SWITCH("/converge/device/turnRecord", "信令服务-设置录像开关", HttpMethod.POST, 1),

    CHANNEL_RECORD_BATCH_SETTING("/converge/record/plans", "信令服务-批量设置录像计划", HttpMethod.POST, 1),

    SERVICES_STREAMS("/converge/services/streams", "信令服务-查询流媒体服务下流列表", HttpMethod.POST, 1),

    SERVICES_STREAMS_SUBSCRIPTIONS("/converge/services/streams/subscriptions", "信令服务-查询订阅者列表", HttpMethod.POST, 1),

    CHANNEL_CHANGE_SERVICE("/converge/channel/changeService", "信令服务-更改通道绑定的流媒体服务", HttpMethod.POST, 1),

    CHANGE_MEDIA_SERVICE("", "信令服务-调度设备流媒体", HttpMethod.POST, 1),

    SERVICE_SMS("/converge/services/sms", "信令服务-查询流媒体流服务列表", HttpMethod.POST, 1),

    // 云台相关
    PTZ_START("/converge/ptz/start", "信令服务-PTZ开始", HttpMethod.POST, 1),

    PTZ_STOP("/converge/ptz/stop", "信令服务-PTZ结束", HttpMethod.POST, 1),

    //预置点
    PRE_SET_LIST("/converge/ptz/list", "信令服务-预置点列表", HttpMethod.POST, 1),

    PRE_SET("/converge/ptz/set", "信令服务-设置预置点", HttpMethod.POST, 1),

    PRE_SET_JUMP("/converge/ptz/jump", "信令服务-跳转预置点", HttpMethod.POST, 1),

    PRE_SET_DELETE("/converge/ptz/delete", "信令服务-删除预置点", HttpMethod.POST, 1),

    //看守位
    GET_HOME_POSITION("/converge/gb2022/gethomeposition", "信令服务-获取看守位", HttpMethod.POST, 1),

    CTRL_HOME_POSITION("/converge/gb2022/ctrlhomeposition", "信令服务-设置看守位", HttpMethod.POST, 1),

    //对讲
    DEVICE_RTC_CONN("/converge/device/rtc", "信令服务-建立设备RTC连接", HttpMethod.POST, 1),

    DEVICE_GB_TALK("/converge/gb2022/talk", "信令服务-国标对讲", HttpMethod.POST, 1),

    //快照
    DEVICE_SNAPSHOT("/converge/channel/snapshot", "信令服务-获取快照", HttpMethod.POST, 1),

    //rms信息
    GET_RMS_RECORDS("", "信令服务-rms录制列表清单", HttpMethod.POST, 1),


    //AI相关

    ADD_EMPLOYEE("/employee/add", "信令服务-新增人员", HttpMethod.POST, 1),

    DELETE_EMPLOYEE("/employee/delete", "信令服务-删除人员", HttpMethod.POST, 1),

    SEARCH_EMPLOYEE("/employee/search", "信令服务-查询人员", HttpMethod.POST, 1),

    ADD_CAR_INFO("/car/save", "信令服务-新增车辆信息", HttpMethod.POST, 1),

    DELETE_CAR_INFO("/car/delete", "信令服务-删除车辆信息", HttpMethod.POST, 1),

    CHANNELS_PUSH("/converge/channel/virtual/push/url", "信令服务-获取推流地址", HttpMethod.POST, 1),

    CHANNELS_VIRTUAL_SAVE("/converge/channel/virtual/add", "信令服务-添加或修改虚拟通道", HttpMethod.POST, 1),

    CHANNELS_VIRTUAL_DEL("/converge/channel/virtual/delete", "信令服务-删除虚拟通道", HttpMethod.POST, 1),

    SEARCH_CAR_INFO("/car/search", "信令服务-查询车辆信息", HttpMethod.POST, 1),

    //级联相关

    API_CASCADES("/cascades", "信令服务-级联上级平台列表", HttpMethod.GET, 2),

    GB_SUBSCRIBES("/converge/device/subscribe", "信令服务-订阅国标下级平台", HttpMethod.POST, 1),

    CONVERGE_CASCADE_MODIFY("/converge/cascade/modify", "信令服务-级联新增修改", HttpMethod.POST, 1),

    CONVERGE_CASCADE_DELETE("/converge/cascade/delete", "信令服务-级联删除", HttpMethod.POST, 1),

    CONVERGE_CASCADE_LIST("/converge/cascade/list", "信令服务-级联列表", HttpMethod.POST, 1),

    CONVERGE_PLATFORM_MODIFY("/converge/platform/modify", "下级平台新增修改", HttpMethod.POST, 1),
    CONVERGE_PLATFORM_ENABLED("/converge/cascade/enabled", "级联启用禁用", HttpMethod.POST, 1),

    CONVERGE_DEVICE_SUBSCRIBE("/converge/device/subscribe", "设备订阅", HttpMethod.POST, 1),

    CONVERGE_PLATFORM_LIST("/converge/platform/list", "下级平台列表", HttpMethod.POST, 1),

    CONVERGE_PLATFORM_DELETE("/converge/platform/delete", "下级平台删除", HttpMethod.POST, 1),

    CONVERGE_CASCADE_SHARE("/converge/cascade/share", "级联共享", HttpMethod.POST, 1),

    // 获取设备参数
    CONVERGE_GET_PARAMETERS("/sd/parameters/get", "获取设备参数", HttpMethod.POST, 1),

    CONVERGE_SET_PARAMETERS("/sd/parameters/set", "设置设备参数", HttpMethod.POST, 1),

    CONVERGE_GET_SD_STREAM_URL("/sd/live", "获取赛达直播地址", HttpMethod.POST, 1),
    CONVERGE_GET_SD_VOICE_URL("/sd/voice", "获取赛达语音地址", HttpMethod.POST, 1),
    CONVERGE_GET_GB_STREAM_URL("/gb/live", "获取国标直播地址", HttpMethod.POST, 1),
    CONVERGE_GET_SD_DOWNLOAD_URL("/sd/getLogUrl", "获取日志下载地址", HttpMethod.POST, 1),
    ;


    /*
     * 接口路径
     */
    private final String path;
    /*
     * 接口描述
     */
    private final String des;
    /*
     * 接口请求方式
     */
    private final HttpMethod httpMethod;
    /*
     * Content-Type：1-application/json；2-form
     */
    private final Integer contentType;
}
